from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.personnel import Personnel, Presence, Planning, Conge, Competence, CompetencePersonnel
from database import db
from datetime import datetime, date, timedelta

personnel_bp = Blueprint('personnel', __name__)

@personnel_bp.route('/')
@login_required
def index():
    """Vue d'ensemble du personnel"""
    # Personnel actif
    personnel_actif = Personnel.query.filter_by(statut='Actif').count()
    
    # Présences du jour
    today = date.today()
    presences_today = Presence.query.filter_by(date=today).all()
    personnel_present = len([p for p in presences_today if p.statut == 'Présent'])
    
    # Congés en cours
    conges_en_cours = Conge.query.filter(
        Conge.date_debut <= today,
        Conge.date_fin >= today,
        Conge.statut == 'Approuvé'
    ).count()
    
    # Planning de la semaine
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)
    planning_semaine = Planning.query.filter(
        Planning.date_debut >= week_start,
        Planning.date_fin <= week_end
    ).all()
    
    return render_template('personnel/index.html',
                         personnel_actif=personnel_actif,
                         personnel_present=personnel_present,
                         conges_en_cours=conges_en_cours,
                         planning_semaine=planning_semaine)

@personnel_bp.route('/employes')
@login_required
def employes():
    """Liste des employés"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    departement = request.args.get('departement', '')
    
    query = Personnel.query
    
    if search:
        query = query.filter(
            (Personnel.nom.contains(search)) |
            (Personnel.prenom.contains(search)) |
            (Personnel.matricule.contains(search))
        )
    
    if departement:
        query = query.filter_by(departement=departement)
    
    employes = query.order_by(Personnel.nom).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # Départements pour le filtre
    departements = db.session.query(Personnel.departement).distinct().all()
    departements = [d[0] for d in departements if d[0]]
    
    return render_template('personnel/employes.html',
                         employes=employes,
                         search=search,
                         departement=departement,
                         departements=departements)

@personnel_bp.route('/employe/<int:employe_id>')
@login_required
def detail_employe(employe_id):
    """Détail d'un employé"""
    employe = Personnel.query.get_or_404(employe_id)
    
    # Présences du mois
    today = date.today()
    start_month = today.replace(day=1)
    presences_mois = Presence.query.filter(
        Presence.personnel_id == employe_id,
        Presence.date >= start_month
    ).order_by(Presence.date.desc()).all()
    
    # Congés de l'année
    start_year = today.replace(month=1, day=1)
    conges_annee = Conge.query.filter(
        Conge.personnel_id == employe_id,
        Conge.date_debut >= start_year
    ).order_by(Conge.date_debut.desc()).all()
    
    # Compétences
    competences = CompetencePersonnel.query.filter_by(personnel_id=employe_id).all()
    
    return render_template('personnel/detail_employe.html',
                         employe=employe,
                         presences_mois=presences_mois,
                         conges_annee=conges_annee,
                         competences=competences)

@personnel_bp.route('/nouvel_employe', methods=['GET', 'POST'])
@login_required
def nouvel_employe():
    """Ajout d'un nouvel employé"""
    if request.method == 'POST':
        try:
            employe = Personnel(
                matricule=request.form['matricule'],
                nom=request.form['nom'],
                prenom=request.form['prenom'],
                date_naissance=datetime.strptime(request.form['date_naissance'], '%Y-%m-%d').date() if request.form['date_naissance'] else None,
                lieu_naissance=request.form['lieu_naissance'],
                adresse=request.form['adresse'],
                telephone=request.form['telephone'],
                email=request.form['email'],
                poste=request.form['poste'],
                departement=request.form['departement'],
                niveau_competence=request.form['niveau_competence'],
                date_embauche=datetime.strptime(request.form['date_embauche'], '%Y-%m-%d').date(),
                salaire=float(request.form['salaire']) if request.form['salaire'] else None
            )
            
            db.session.add(employe)
            db.session.commit()
            flash('Employé ajouté avec succès', 'success')
            return redirect(url_for('personnel.employes'))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de l\'ajout: {str(e)}', 'error')
    
    return render_template('personnel/nouvel_employe.html')

@personnel_bp.route('/presences')
@login_required
def presences():
    """Gestion des présences"""
    today = date.today()
    date_filter = request.args.get('date', today.strftime('%Y-%m-%d'))
    date_obj = datetime.strptime(date_filter, '%Y-%m-%d').date()
    
    # Présences du jour sélectionné
    presences = Presence.query.filter_by(date=date_obj).all()
    
    # Personnel actif
    personnel_actif = Personnel.query.filter_by(statut='Actif').all()
    
    # Créer un dictionnaire des présences par employé
    presences_dict = {p.personnel_id: p for p in presences}
    
    return render_template('personnel/presences.html',
                         personnel_actif=personnel_actif,
                         presences_dict=presences_dict,
                         date_filter=date_filter)

@personnel_bp.route('/saisie_presence', methods=['POST'])
@login_required
def saisie_presence():
    """Saisie des présences"""
    try:
        date_str = request.form['date']
        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
        
        for personnel_id in request.form.getlist('personnel_ids'):
            # Vérifier si une présence existe déjà
            presence = Presence.query.filter_by(
                personnel_id=personnel_id,
                date=date_obj
            ).first()
            
            if not presence:
                presence = Presence(personnel_id=personnel_id, date=date_obj)
                db.session.add(presence)
            
            # Mettre à jour les données
            presence.heure_arrivee = datetime.strptime(request.form[f'heure_arrivee_{personnel_id}'], '%H:%M').time() if request.form.get(f'heure_arrivee_{personnel_id}') else None
            presence.heure_depart = datetime.strptime(request.form[f'heure_depart_{personnel_id}'], '%H:%M').time() if request.form.get(f'heure_depart_{personnel_id}') else None
            presence.statut = request.form.get(f'statut_{personnel_id}', 'Présent')
            presence.commentaires = request.form.get(f'commentaires_{personnel_id}', '')
            
            # Calculer les heures travaillées
            if presence.heure_arrivee and presence.heure_depart:
                presence.heures_travaillees = presence.duree_travail
        
        db.session.commit()
        flash('Présences enregistrées avec succès', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'enregistrement: {str(e)}', 'error')
    
    return redirect(url_for('personnel.presences', date=date_str))

@personnel_bp.route('/planning')
@login_required
def planning():
    """Planning du personnel"""
    # Semaine courante
    today = date.today()
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)
    
    # Récupérer le planning de la semaine
    planning_semaine = Planning.query.filter(
        Planning.date_debut >= week_start,
        Planning.date_fin <= week_end
    ).all()
    
    # Personnel actif
    personnel_actif = Personnel.query.filter_by(statut='Actif').all()
    
    return render_template('personnel/planning.html',
                         planning_semaine=planning_semaine,
                         personnel_actif=personnel_actif,
                         week_start=week_start,
                         week_end=week_end)

@personnel_bp.route('/conges')
@login_required
def conges():
    """Gestion des congés"""
    page = request.args.get('page', 1, type=int)
    statut_filter = request.args.get('statut', '')
    
    query = Conge.query
    
    if statut_filter:
        query = query.filter_by(statut=statut_filter)
    
    conges = query.order_by(Conge.date_demande.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('personnel/conges.html',
                         conges=conges,
                         statut_filter=statut_filter)

@personnel_bp.route('/demande_conge', methods=['GET', 'POST'])
@login_required
def demande_conge():
    """Demande de congé"""
    if request.method == 'POST':
        try:
            date_debut = datetime.strptime(request.form['date_debut'], '%Y-%m-%d').date()
            date_fin = datetime.strptime(request.form['date_fin'], '%Y-%m-%d').date()
            nombre_jours = (date_fin - date_debut).days + 1
            
            conge = Conge(
                personnel_id=request.form['personnel_id'],
                type_conge=request.form['type_conge'],
                date_debut=date_debut,
                date_fin=date_fin,
                nombre_jours=nombre_jours,
                motif=request.form['motif']
            )
            
            db.session.add(conge)
            db.session.commit()
            flash('Demande de congé enregistrée avec succès', 'success')
            return redirect(url_for('personnel.conges'))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de l\'enregistrement: {str(e)}', 'error')
    
    personnel_actif = Personnel.query.filter_by(statut='Actif').all()
    return render_template('personnel/demande_conge.html', personnel_actif=personnel_actif)
