from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.stocks import Stock, MatierePremiereType, Fournisseur, Livraison, DetailLivraison, MouvementStock
from models.personnel import Personnel
from database import db
from datetime import datetime, date

stocks_bp = Blueprint('stocks', __name__)

@stocks_bp.route('/')
@login_required
def index():
    """Vue d'ensemble des stocks"""
    # Stocks actuels
    stocks = Stock.query.all()
    
    # Stocks nécessitant un réapprovisionnement
    stocks_reappro = [s for s in stocks if s.besoin_reapprovisionnement]
    
    # Stocks critiques
    stocks_critiques = [s for s in stocks if s.niveau_critique]
    
    # Livraisons en attente
    livraisons_attente = Livraison.query.filter_by(statut='En transit').all()
    
    # Valeur totale des stocks
    valeur_totale = sum(s.valeur_stock or 0 for s in stocks)
    
    return render_template('stocks/index.html',
                         stocks=stocks,
                         stocks_reappro=stocks_reappro,
                         stocks_critiques=stocks_critiques,
                         livraisons_attente=livraisons_attente,
                         valeur_totale=valeur_totale)

@stocks_bp.route('/matieres_premieres')
@login_required
def matieres_premieres():
    """Gestion des matières premières"""
    matieres = MatierePremiereType.query.all()
    return render_template('stocks/matieres_premieres.html', matieres=matieres)

@stocks_bp.route('/nouvelle_matiere', methods=['GET', 'POST'])
@login_required
def nouvelle_matiere():
    """Ajout d'une nouvelle matière première"""
    if request.method == 'POST':
        try:
            matiere = MatierePremiereType(
                nom=request.form['nom'],
                description=request.form['description'],
                unite_mesure=request.form['unite_mesure'],
                densite=float(request.form['densite']) if request.form['densite'] else None,
                specifications=request.form['specifications']
            )
            
            db.session.add(matiere)
            db.session.flush()  # Pour obtenir l'ID
            
            # Créer le stock initial
            stock = Stock(
                matiere_premiere_id=matiere.id,
                quantite_actuelle=0,
                quantite_minimum=float(request.form['quantite_minimum']) if request.form['quantite_minimum'] else 0,
                quantite_maximum=float(request.form['quantite_maximum']) if request.form['quantite_maximum'] else 0,
                quantite_securite=float(request.form['quantite_securite']) if request.form['quantite_securite'] else 0,
                zone_stockage=request.form['zone_stockage'],
                emplacement=request.form['emplacement']
            )
            
            db.session.add(stock)
            db.session.commit()
            flash('Matière première ajoutée avec succès', 'success')
            return redirect(url_for('stocks.matieres_premieres'))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de l\'ajout: {str(e)}', 'error')
    
    return render_template('stocks/nouvelle_matiere.html')

@stocks_bp.route('/stock/<int:stock_id>')
@login_required
def detail_stock(stock_id):
    """Détail d'un stock"""
    stock = Stock.query.get_or_404(stock_id)
    
    # Mouvements récents
    mouvements = MouvementStock.query.filter_by(stock_id=stock_id).order_by(
        MouvementStock.date_mouvement.desc()
    ).limit(50).all()
    
    return render_template('stocks/detail_stock.html',
                         stock=stock,
                         mouvements=mouvements)

@stocks_bp.route('/mouvement_stock', methods=['GET', 'POST'])
@login_required
def mouvement_stock():
    """Enregistrement d'un mouvement de stock"""
    if request.method == 'POST':
        try:
            stock_id = request.form['stock_id']
            stock = Stock.query.get(stock_id)
            
            quantite = float(request.form['quantite'])
            type_mouvement = request.form['type_mouvement']
            
            # Ajuster la quantité selon le type de mouvement
            if type_mouvement == 'Sortie':
                quantite = -abs(quantite)
            elif type_mouvement == 'Entrée':
                quantite = abs(quantite)
            
            # Vérifier que le stock ne devient pas négatif
            nouvelle_quantite = stock.quantite_actuelle + quantite
            if nouvelle_quantite < 0:
                flash('Stock insuffisant pour cette sortie', 'error')
                return redirect(url_for('stocks.mouvement_stock'))
            
            # Créer le mouvement
            mouvement = MouvementStock(
                stock_id=stock_id,
                type_mouvement=type_mouvement,
                quantite=quantite,
                quantite_avant=stock.quantite_actuelle,
                quantite_apres=nouvelle_quantite,
                reference_document=request.form['reference_document'],
                motif=request.form['motif'],
                utilisateur_id=current_user.id
            )
            
            # Mettre à jour le stock
            stock.quantite_actuelle = nouvelle_quantite
            stock.derniere_mise_a_jour = datetime.utcnow()
            
            # Recalculer la valeur du stock si c'est une entrée avec prix
            if type_mouvement == 'Entrée' and request.form.get('prix_unitaire'):
                prix_unitaire = float(request.form['prix_unitaire'])
                ancienne_valeur = stock.valeur_stock or 0
                nouvelle_valeur = ancienne_valeur + (abs(quantite) * prix_unitaire)
                stock.valeur_stock = nouvelle_valeur
                stock.prix_moyen = nouvelle_valeur / stock.quantite_actuelle if stock.quantite_actuelle > 0 else 0
            
            db.session.add(mouvement)
            db.session.commit()
            flash('Mouvement de stock enregistré avec succès', 'success')
            return redirect(url_for('stocks.index'))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de l\'enregistrement: {str(e)}', 'error')
    
    stocks = Stock.query.all()
    return render_template('stocks/mouvement_stock.html', stocks=stocks)

@stocks_bp.route('/fournisseurs')
@login_required
def fournisseurs():
    """Gestion des fournisseurs"""
    fournisseurs = Fournisseur.query.filter_by(actif=True).all()
    return render_template('stocks/fournisseurs.html', fournisseurs=fournisseurs)

@stocks_bp.route('/nouveau_fournisseur', methods=['GET', 'POST'])
@login_required
def nouveau_fournisseur():
    """Ajout d'un nouveau fournisseur"""
    if request.method == 'POST':
        try:
            fournisseur = Fournisseur(
                nom=request.form['nom'],
                adresse=request.form['adresse'],
                telephone=request.form['telephone'],
                email=request.form['email'],
                contact_principal=request.form['contact_principal'],
                delai_livraison=int(request.form['delai_livraison']) if request.form['delai_livraison'] else None,
                conditions_paiement=request.form['conditions_paiement'],
                note_qualite=float(request.form['note_qualite']) if request.form['note_qualite'] else None
            )
            
            db.session.add(fournisseur)
            db.session.commit()
            flash('Fournisseur ajouté avec succès', 'success')
            return redirect(url_for('stocks.fournisseurs'))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de l\'ajout: {str(e)}', 'error')
    
    return render_template('stocks/nouveau_fournisseur.html')

@stocks_bp.route('/livraisons')
@login_required
def livraisons():
    """Gestion des livraisons"""
    page = request.args.get('page', 1, type=int)
    statut_filter = request.args.get('statut', '')
    
    query = Livraison.query
    
    if statut_filter:
        query = query.filter_by(statut=statut_filter)
    
    livraisons = query.order_by(Livraison.date_creation.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('stocks/livraisons.html',
                         livraisons=livraisons,
                         statut_filter=statut_filter)

@stocks_bp.route('/nouvelle_livraison', methods=['GET', 'POST'])
@login_required
def nouvelle_livraison():
    """Enregistrement d'une nouvelle livraison"""
    if request.method == 'POST':
        try:
            livraison = Livraison(
                numero_livraison=request.form['numero_livraison'],
                fournisseur_id=request.form['fournisseur_id'],
                date_commande=datetime.strptime(request.form['date_commande'], '%Y-%m-%d').date() if request.form['date_commande'] else None,
                date_livraison_prevue=datetime.strptime(request.form['date_livraison_prevue'], '%Y-%m-%d').date() if request.form['date_livraison_prevue'] else None,
                cout_total=float(request.form['cout_total']) if request.form['cout_total'] else 0,
                cout_transport=float(request.form['cout_transport']) if request.form['cout_transport'] else 0
            )
            
            db.session.add(livraison)
            db.session.commit()
            flash('Livraison enregistrée avec succès', 'success')
            return redirect(url_for('stocks.detail_livraison', livraison_id=livraison.id))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de l\'enregistrement: {str(e)}', 'error')
    
    fournisseurs = Fournisseur.query.filter_by(actif=True).all()
    return render_template('stocks/nouvelle_livraison.html', fournisseurs=fournisseurs)

@stocks_bp.route('/livraison/<int:livraison_id>')
@login_required
def detail_livraison(livraison_id):
    """Détail d'une livraison"""
    livraison = Livraison.query.get_or_404(livraison_id)
    return render_template('stocks/detail_livraison.html', livraison=livraison)

@stocks_bp.route('/api/stock_evolution/<int:stock_id>')
@login_required
def api_stock_evolution(stock_id):
    """API pour l'évolution d'un stock"""
    from datetime import timedelta
    
    days = request.args.get('days', 30, type=int)
    end_date = date.today()
    start_date = end_date - timedelta(days=days)
    
    mouvements = MouvementStock.query.filter(
        MouvementStock.stock_id == stock_id,
        MouvementStock.date_mouvement >= start_date
    ).order_by(MouvementStock.date_mouvement).all()
    
    data = {
        'dates': [m.date_mouvement.strftime('%Y-%m-%d') for m in mouvements],
        'quantites': [float(m.quantite_apres) for m in mouvements],
        'mouvements': [float(m.quantite) for m in mouvements]
    }
    
    return jsonify(data)
