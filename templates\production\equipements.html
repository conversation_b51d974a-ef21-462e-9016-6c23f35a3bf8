{% extends "base.html" %}

{% block title %}Équipements - Gestion Unité Phosphate{% endblock %}
{% block page_title %}Gestion des Équipements{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4>Parc d'équipements</h4>
            <div>
                <a href="{{ url_for('production.nouvel_equipement') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Nouvel équipement
                </a>
                <a href="{{ url_for('maintenance.preventive') }}" class="btn btn-outline-warning">
                    <i class="fas fa-calendar-check"></i> Planning maintenance
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filtres -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="unite" class="form-label">Unité de production</label>
                <select class="form-select" id="unite" name="unite">
                    <option value="">Toutes les unités</option>
                    {% for unite in unites %}
                    <option value="{{ unite.id }}" {% if unite.id|string == unite_filter %}selected{% endif %}>
                        {{ unite.nom }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="type" class="form-label">Type d'équipement</label>
                <select class="form-select" id="type" name="type">
                    <option value="">Tous les types</option>
                    {% for type in types_equipement %}
                    <option value="{{ type }}" {% if type == type_filter %}selected{% endif %}>
                        {{ type }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="statut" class="form-label">Statut</label>
                <select class="form-select" id="statut" name="statut">
                    <option value="">Tous les statuts</option>
                    <option value="Opérationnel" {% if statut_filter == 'Opérationnel' %}selected{% endif %}>Opérationnel</option>
                    <option value="En panne" {% if statut_filter == 'En panne' %}selected{% endif %}>En panne</option>
                    <option value="Maintenance" {% if statut_filter == 'Maintenance' %}selected{% endif %}>En maintenance</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search"></i> Filtrer
                </button>
                <a href="{{ url_for('production.equipements') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Effacer
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Vue en cartes des équipements -->
<div class="row">
    {% for equipement in equipements %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 equipement-card" data-statut="{{ equipement.statut }}">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-{{ 'cog' if equipement.type_equipement == 'Concasseur' else 'tint' if equipement.type_equipement == 'Pompe' else 'filter' if equipement.type_equipement == 'Filtre' else 'tools' }}"></i>
                    {{ equipement.nom }}
                </h6>
                <span class="badge bg-{{ 'success' if equipement.statut == 'Opérationnel' else 'danger' if equipement.statut == 'En panne' else 'warning' }}">
                    {{ equipement.statut }}
                </span>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">Type</small>
                        <div class="fw-bold">{{ equipement.type_equipement }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">N° Série</small>
                        <div class="fw-bold">{{ equipement.numero_serie }}</div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">Unité</small>
                        <div class="fw-bold">{{ equipement.unite_production.nom if equipement.unite_production else 'N/A' }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Installation</small>
                        <div class="fw-bold">{{ equipement.date_installation.strftime('%d/%m/%Y') if equipement.date_installation else 'N/A' }}</div>
                    </div>
                </div>

                {% if equipement.fabricant %}
                <div class="mb-2">
                    <small class="text-muted">Fabricant</small>
                    <div>{{ equipement.fabricant }}</div>
                </div>
                {% endif %}

                {% if equipement.specifications %}
                <div class="mb-3">
                    <small class="text-muted">Spécifications</small>
                    <div class="text-truncate" title="{{ equipement.specifications }}">
                        {{ equipement.specifications[:50] }}{% if equipement.specifications|length > 50 %}...{% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Indicateurs de maintenance -->
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <div class="h6 text-info">{{ equipement.maintenances|length }}</div>
                            <small class="text-muted">Interventions</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <div class="h6 text-warning">
                                {% set maintenance_due = equipement.prochaine_maintenance_due %}
                                {% if maintenance_due %}
                                    {{ (maintenance_due - date.today()).days }}j
                                {% else %}
                                    N/A
                                {% endif %}
                            </div>
                            <small class="text-muted">Proch. maint.</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="h6 text-success">
                            {% if equipement.date_installation %}
                                {{ ((date.today() - equipement.date_installation).days / 365.25)|round(1) }}
                            {% else %}
                                N/A
                            {% endif %}
                        </div>
                        <small class="text-muted">Âge (ans)</small>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="btn-group w-100" role="group">
                    <a href="{{ url_for('production.detail_equipement', equipement_id=equipement.id) }}" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> Détails
                    </a>
                    <a href="{{ url_for('maintenance.nouvelle_intervention', equipement_id=equipement.id) }}" 
                       class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-wrench"></i> Maintenance
                    </a>
                    <a href="{{ url_for('production.modifier_equipement', equipement_id=equipement.id) }}" 
                       class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-edit"></i> Modifier
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

{% if not equipements %}
<div class="text-center py-5">
    <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
    <h5 class="text-muted">Aucun équipement trouvé</h5>
    <p class="text-muted">
        {% if unite_filter or type_filter or statut_filter %}
            Aucun équipement ne correspond à vos critères de recherche.
        {% else %}
            Commencez par ajouter des équipements à votre parc.
        {% endif %}
    </p>
    <a href="{{ url_for('production.nouvel_equipement') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> Ajouter un équipement
    </a>
</div>
{% endif %}

<!-- Statistiques du parc -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">Statistiques du parc d'équipements</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-primary">{{ equipements|length }}</h4>
                            <small class="text-muted">Total équipements</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-success">
                                {{ equipements | selectattr('statut', 'equalto', 'Opérationnel') | list | length }}
                            </h4>
                            <small class="text-muted">Opérationnels</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-danger">
                                {{ equipements | selectattr('statut', 'equalto', 'En panne') | list | length }}
                            </h4>
                            <small class="text-muted">En panne</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-warning">
                                {{ equipements | selectattr('statut', 'equalto', 'Maintenance') | list | length }}
                            </h4>
                            <small class="text-muted">En maintenance</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-info">
                                {% set age_moyen = equipements | selectattr('date_installation') | map(attribute='date_installation') | list %}
                                {% if age_moyen %}
                                    {{ ((age_moyen | map('age_in_years') | sum) / (age_moyen | length)) | round(1) }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </h4>
                            <small class="text-muted">Âge moyen (ans)</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-secondary">
                            {% if equipements %}
                                {{ ((equipements | selectattr('statut', 'equalto', 'Opérationnel') | list | length) / (equipements | length) * 100) | round(1) }}%
                            {% else %}
                                0%
                            {% endif %}
                        </h4>
                        <small class="text-muted">Disponibilité</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alertes maintenance -->
{% set equipements_maintenance_due = equipements | selectattr('maintenance_due', 'equalto', True) | list %}
{% if equipements_maintenance_due %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="alert alert-warning">
            <h6><i class="fas fa-exclamation-triangle"></i> Maintenance due</h6>
            <p class="mb-2">Les équipements suivants nécessitent une maintenance :</p>
            <ul class="mb-0">
                {% for equipement in equipements_maintenance_due %}
                <li>
                    <strong>{{ equipement.nom }}</strong> ({{ equipement.type_equipement }}) - 
                    <a href="{{ url_for('maintenance.nouvelle_intervention', equipement_id=equipement.id) }}" class="btn btn-sm btn-warning">
                        Planifier maintenance
                    </a>
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
.equipement-card {
    transition: transform 0.2s ease-in-out;
    border-left: 4px solid #dee2e6;
}

.equipement-card[data-statut="Opérationnel"] {
    border-left-color: #28a745;
}

.equipement-card[data-statut="En panne"] {
    border-left-color: #dc3545;
}

.equipement-card[data-statut="Maintenance"] {
    border-left-color: #ffc107;
}

.equipement-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.card-footer .btn-group .btn {
    border-radius: 0;
}

.card-footer .btn-group .btn:first-child {
    border-radius: 0.375rem 0 0 0.375rem;
}

.card-footer .btn-group .btn:last-child {
    border-radius: 0 0.375rem 0.375rem 0;
}

.text-truncate {
    max-width: 100%;
}
</style>
{% endblock %}
