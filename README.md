# Phosphate Manager - Système de Gestion d'Unité Phosphate

Application web complète pour la gestion d'une unité de production de phosphate, développée avec Flask et SQLite.

## 🚀 Fonctionnalités

### 📊 Production
- **Trois unités de production** : Concassage, Lavage, Système d'eau
- Suivi des données de production quotidiennes
- Gestion des équipements et de leur statut
- Planning de production et ordonnancement
- Calcul automatique des rendements et taux de réalisation

### 🔧 Maintenance
- **Maintenance corrective** : Gestion des pannes et réparations
- **Maintenance préventive** : Planification et suivi des maintenances programmées
- Gestion des pièces de rechange et stocks
- Suivi des coûts et durées d'intervention
- Historique complet des interventions

### 👥 Personnel
- Gestion complète des employés
- **Suivi des présences** : Pointage quotidien, heures travaillées
- **Planning** : Affectation du personnel aux postes
- Gestion des congés et absences
- Système de compétences et formations

### 📦 Stocks
- Gestion des matières premières
- Suivi des mouvements de stock (entrées/sorties)
- Gestion des fournisseurs et livraisons
- Alertes de réapprovisionnement
- Valorisation des stocks

### ✅ Qualité
- Définition des paramètres de qualité
- Contrôles qualité sur les produits
- Actions correctives en cas de non-conformité
- Certificats d'analyse
- Indicateurs et tendances qualité

### 📈 Rapports et Tableaux de Bord
- Tableaux de bord interactifs avec graphiques
- Rapports détaillés par module
- Export Excel des données
- Indicateurs de performance (KPI)

## 🛠️ Installation

### Prérequis
- Python 3.8 ou supérieur
- pip (gestionnaire de paquets Python)

### Étapes d'installation

1. **Cloner ou télécharger le projet**
   ```bash
   # Si vous avez git
   git clone <url-du-projet>
   cd Application\ gestion\ phoshate
   
   # Ou extraire l'archive ZIP dans le dossier
   ```

2. **Créer un environnement virtuel (recommandé)**
   ```bash
   python -m venv venv
   
   # Activer l'environnement virtuel
   # Sur Windows:
   venv\Scripts\activate
   # Sur Linux/Mac:
   source venv/bin/activate
   ```

3. **Installer les dépendances**
   ```bash
   pip install -r requirements.txt
   ```

4. **Initialiser la base de données**
   ```bash
   python init_db.py
   ```
   
   Cette commande va créer :
   - La base de données SQLite
   - Un utilisateur administrateur (admin/admin123)
   - Des données de démonstration

5. **Lancer l'application**
   ```bash
   python run.py
   ```

6. **Accéder à l'application**
   - Ouvrir un navigateur web
   - Aller à l'adresse : `http://localhost:5000`
   - Se connecter avec : **admin** / **admin123**

## 📋 Utilisation

### Première connexion
1. Utilisateur : `admin`
2. Mot de passe : `admin123`
3. Changer le mot de passe depuis le profil utilisateur

### Navigation
- **Tableau de bord** : Vue d'ensemble de l'activité
- **Production** : Gestion des unités et saisie des données
- **Maintenance** : Interventions et planning préventif
- **Personnel** : Gestion des employés et présences
- **Stocks** : Matières premières et mouvements
- **Qualité** : Contrôles et actions correctives
- **Rapports** : Analyses et exports

### Données de démonstration
L'application est livrée avec des données de test :
- 3 unités de production (Concassage, Lavage, Système d'eau)
- 6 équipements répartis sur les unités
- 4 employés avec différents postes
- Matières premières et stocks initiaux
- Paramètres de qualité configurés

## 🏗️ Architecture

### Structure du projet
```
Application gestion phoshate/
├── app.py                 # Application Flask principale
├── run.py                 # Script de lancement
├── init_db.py            # Initialisation de la base de données
├── config.py             # Configuration
├── requirements.txt      # Dépendances Python
├── models/               # Modèles de données
│   ├── user.py          # Utilisateurs
│   ├── production.py    # Production et équipements
│   ├── maintenance.py   # Maintenance et pièces
│   ├── personnel.py     # Personnel et présences
│   ├── stocks.py        # Stocks et fournisseurs
│   └── qualite.py       # Qualité et contrôles
├── routes/              # Routes et contrôleurs
│   ├── auth.py          # Authentification
│   ├── production.py    # Routes production
│   ├── maintenance.py   # Routes maintenance
│   ├── personnel.py     # Routes personnel
│   ├── stocks.py        # Routes stocks
│   ├── qualite.py       # Routes qualité
│   └── rapports.py      # Routes rapports
└── templates/           # Templates HTML
    ├── base.html        # Template de base
    ├── dashboard.html   # Tableau de bord
    └── auth/           # Templates authentification
```

### Technologies utilisées
- **Backend** : Flask (Python)
- **Base de données** : SQLite
- **Frontend** : Bootstrap 5, Chart.js, Plotly.js
- **ORM** : SQLAlchemy
- **Authentification** : Flask-Login

## 🔧 Configuration

### Variables d'environnement
Créer un fichier `.env` pour personnaliser la configuration :
```
SECRET_KEY=votre-cle-secrete-unique
FLASK_ENV=development
DATABASE_URL=sqlite:///phosphate_custom.db
```

### Base de données
- **Développement** : SQLite (fichier local)
- **Production** : Configurable via DATABASE_URL

## 📊 Fonctionnalités avancées

### Tableaux de bord
- Graphiques interactifs en temps réel
- Indicateurs de performance (KPI)
- Alertes et notifications

### Rapports
- Export Excel des données
- Rapports personnalisables par période
- Graphiques et analyses statistiques

### Sécurité
- Authentification par utilisateur
- Gestion des rôles (admin, superviseur, opérateur)
- Sessions sécurisées

## 🚀 Déploiement en production

### Serveur web
Pour un déploiement en production, utiliser un serveur WSGI comme Gunicorn :
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Base de données
Pour la production, migrer vers PostgreSQL ou MySQL :
```bash
# Exemple PostgreSQL
pip install psycopg2
export DATABASE_URL=postgresql://user:password@localhost/phosphate_db
```

## 🤝 Support

### Problèmes courants
1. **Erreur de base de données** : Exécuter `python init_db.py`
2. **Port 5000 occupé** : Modifier le port dans `run.py`
3. **Modules manquants** : Vérifier `pip install -r requirements.txt`

### Logs
Les logs de l'application sont affichés dans la console en mode développement.

## 📝 Licence

Ce projet est développé pour la gestion d'unités de production de phosphate.

---

**Phosphate Manager** - Système de gestion intégré pour l'industrie du phosphate
Version 1.0 - 2024
