{% extends "base.html" %}

{% block title %}Profil - Gestion Unité Phosphate{% endblock %}
{% block page_title %}Mon Profil{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-user-circle fa-5x text-primary"></i>
                </div>
                <h4>{{ user.prenom }} {{ user.nom }}</h4>
                <p class="text-muted">{{ user.role|title }}</p>
                <p class="text-muted">{{ user.departement }}</p>
                
                <div class="mt-4">
                    <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-primary">
                        <i class="fas fa-key"></i> Changer le mot de passe
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i>
                    Informations de connexion
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12 mb-2">
                        <small class="text-muted">Nom d'utilisateur</small>
                        <div>{{ user.username }}</div>
                    </div>
                    <div class="col-12 mb-2">
                        <small class="text-muted">Email</small>
                        <div>{{ user.email or 'Non renseigné' }}</div>
                    </div>
                    <div class="col-12 mb-2">
                        <small class="text-muted">Dernière connexion</small>
                        <div>
                            {% if user.derniere_connexion %}
                                {{ user.derniere_connexion.strftime('%d/%m/%Y à %H:%M') }}
                            {% else %}
                                Première connexion
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-12">
                        <small class="text-muted">Compte créé le</small>
                        <div>{{ user.date_creation.strftime('%d/%m/%Y') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line"></i>
                    Activité récente
                </h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Connexion au système</h6>
                            <p class="timeline-text">Connexion réussie depuis l'adresse IP *************</p>
                            <small class="text-muted">Il y a 2 heures</small>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Saisie de production</h6>
                            <p class="timeline-text">Données de production saisies pour l'unité de concassage</p>
                            <small class="text-muted">Il y a 4 heures</small>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker bg-warning"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Rapport généré</h6>
                            <p class="timeline-text">Rapport de maintenance exporté en Excel</p>
                            <small class="text-muted">Hier à 16:30</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cog"></i>
                    Préférences
                </h6>
            </div>
            <div class="card-body">
                <form>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Langue</label>
                                <select class="form-select">
                                    <option selected>Français</option>
                                    <option>English</option>
                                    <option>العربية</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Fuseau horaire</label>
                                <select class="form-select">
                                    <option selected>GMT+1 (Maroc)</option>
                                    <option>GMT+0 (UTC)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="notifications" checked>
                            <label class="form-check-label" for="notifications">
                                Recevoir les notifications par email
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="alerts" checked>
                            <label class="form-check-label" for="alerts">
                                Alertes en temps réel
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Sauvegarder les préférences
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 5px;
    font-size: 13px;
    color: #6c757d;
}
</style>
{% endblock %}
