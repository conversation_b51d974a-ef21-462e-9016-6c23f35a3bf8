from flask import Flask, render_template, redirect, url_for, flash, request
from flask_login import LoginManager, login_required, current_user
from flask_migrate import Migrate
from datetime import datetime
import os

# Configuration
app = Flask(__name__)
app.config['SECRET_KEY'] = 'votre-cle-secrete-phosphate-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///phosphate_management.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Extensions
from database import db
db.init_app(app)
migrate = Migrate(app, db)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'

# Import des modèles pour que SQLAlchemy les reconnaisse
from models.user import User
from models.production import UniteProduction, ProductionData, Equipement, PlanProduction
from models.maintenance import Maintenance, PlanMaintenance, PieceRechange
from models.personnel import Personnel, Presence, Planning, Conge, Competence, CompetencePersonnel
from models.stocks import Stock, MatierePremiereType, Fournisseur, Livraison, DetailLivraison, MouvementStock
from models.qualite import ParametreQualite, ControleQualite, ResultatControle, ActionCorrective, CertificatAnalyse, IndicateurQualite

# Import des blueprints
from routes.auth import auth_bp
from routes.production import production_bp
from routes.maintenance import maintenance_bp
from routes.personnel import personnel_bp
from routes.stocks import stocks_bp
from routes.qualite import qualite_bp
from routes.rapports import rapports_bp

# Enregistrement des blueprints
app.register_blueprint(auth_bp, url_prefix='/auth')
app.register_blueprint(production_bp, url_prefix='/production')
app.register_blueprint(maintenance_bp, url_prefix='/maintenance')
app.register_blueprint(personnel_bp, url_prefix='/personnel')
app.register_blueprint(stocks_bp, url_prefix='/stocks')
app.register_blueprint(qualite_bp, url_prefix='/qualite')
app.register_blueprint(rapports_bp, url_prefix='/rapports')

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

@app.route('/')
@login_required
def dashboard():
    """Tableau de bord principal"""
    # Données pour le dashboard
    today = datetime.now().date()

    # Production du jour
    production_today = ProductionData.query.filter_by(date=today).all()

    # Maintenances en cours
    maintenances_actives = Maintenance.query.filter_by(statut='En cours').all()

    # Personnel présent
    personnel_present = Personnel.query.filter_by(present=True).count()

    return render_template('dashboard.html',
                         production_today=production_today,
                         maintenances_actives=maintenances_actives,
                         personnel_present=personnel_present)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0', port=5000)
