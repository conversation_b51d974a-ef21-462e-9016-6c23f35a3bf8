from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_login import login_required, current_user
from models.production import ProductionData, UniteProduction
from models.maintenance import Maintenance
from models.personnel import Personnel, Presence
from models.stocks import Stock, MouvementStock
from models.qualite import ControleQualite, IndicateurQualite
from database import db
from datetime import datetime, date, timedelta
import plotly.graph_objs as go
import plotly.utils
import json
import io

rapports_bp = Blueprint('rapports', __name__)

@rapports_bp.route('/')
@login_required
def index():
    """Vue d'ensemble des rapports"""
    return render_template('rapports/index.html')

@rapports_bp.route('/production')
@login_required
def rapport_production():
    """Rapport de production"""
    # Paramètres de date
    date_debut = request.args.get('date_debut', (date.today() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_fin = request.args.get('date_fin', date.today().strftime('%Y-%m-%d'))
    unite_id = request.args.get('unite_id', '')
    
    # Convertir les dates
    date_debut_obj = datetime.strptime(date_debut, '%Y-%m-%d').date()
    date_fin_obj = datetime.strptime(date_fin, '%Y-%m-%d').date()
    
    # Requête de base
    query = ProductionData.query.filter(
        ProductionData.date >= date_debut_obj,
        ProductionData.date <= date_fin_obj
    )
    
    if unite_id:
        query = query.filter_by(unite_id=unite_id)
    
    productions = query.order_by(ProductionData.date).all()
    
    # Calculs statistiques
    total_produit = sum(p.quantite_produite or 0 for p in productions)
    total_planifie = sum(p.quantite_planifiee or 0 for p in productions)
    rendement_moyen = sum(p.rendement or 0 for p in productions) / len(productions) if productions else 0
    taux_realisation = (total_produit / total_planifie * 100) if total_planifie > 0 else 0
    
    # Graphique de production
    if productions:
        dates = [p.date.strftime('%Y-%m-%d') for p in productions]
        quantites = [float(p.quantite_produite or 0) for p in productions]
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=dates, y=quantites, mode='lines+markers', name='Production'))
        fig.update_layout(title='Évolution de la production', xaxis_title='Date', yaxis_title='Quantité (tonnes)')
        graphJSON = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
    else:
        graphJSON = None
    
    # Unités pour le filtre
    unites = UniteProduction.query.all()
    
    return render_template('rapports/production.html',
                         productions=productions,
                         total_produit=total_produit,
                         total_planifie=total_planifie,
                         rendement_moyen=rendement_moyen,
                         taux_realisation=taux_realisation,
                         graphJSON=graphJSON,
                         unites=unites,
                         date_debut=date_debut,
                         date_fin=date_fin,
                         unite_id=unite_id)

@rapports_bp.route('/maintenance')
@login_required
def rapport_maintenance():
    """Rapport de maintenance"""
    # Paramètres de date
    date_debut = request.args.get('date_debut', (date.today() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_fin = request.args.get('date_fin', date.today().strftime('%Y-%m-%d'))
    type_maintenance = request.args.get('type_maintenance', '')
    
    # Convertir les dates
    date_debut_obj = datetime.strptime(date_debut, '%Y-%m-%d').date()
    date_fin_obj = datetime.strptime(date_fin, '%Y-%m-%d').date()
    
    # Requête de base
    query = Maintenance.query.filter(
        Maintenance.date_demande >= date_debut_obj,
        Maintenance.date_demande <= date_fin_obj
    )
    
    if type_maintenance:
        query = query.filter_by(type_maintenance=type_maintenance)
    
    maintenances = query.order_by(Maintenance.date_demande).all()
    
    # Statistiques
    nb_interventions = len(maintenances)
    nb_correctives = len([m for m in maintenances if m.type_maintenance == 'Corrective'])
    nb_preventives = len([m for m in maintenances if m.type_maintenance == 'Préventive'])
    cout_total = sum(m.cout_total or 0 for m in maintenances)
    duree_moyenne = sum(m.duree_reelle or 0 for m in maintenances if m.duree_reelle) / len([m for m in maintenances if m.duree_reelle]) if maintenances else 0
    
    # Graphique répartition corrective/préventive
    if maintenances:
        fig = go.Figure(data=[go.Pie(labels=['Corrective', 'Préventive'], values=[nb_correctives, nb_preventives])])
        fig.update_layout(title='Répartition des maintenances')
        graphJSON = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
    else:
        graphJSON = None
    
    return render_template('rapports/maintenance.html',
                         maintenances=maintenances,
                         nb_interventions=nb_interventions,
                         nb_correctives=nb_correctives,
                         nb_preventives=nb_preventives,
                         cout_total=cout_total,
                         duree_moyenne=duree_moyenne,
                         graphJSON=graphJSON,
                         date_debut=date_debut,
                         date_fin=date_fin,
                         type_maintenance=type_maintenance)

@rapports_bp.route('/personnel')
@login_required
def rapport_personnel():
    """Rapport personnel"""
    # Paramètres de date
    date_debut = request.args.get('date_debut', (date.today() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_fin = request.args.get('date_fin', date.today().strftime('%Y-%m-%d'))
    
    # Convertir les dates
    date_debut_obj = datetime.strptime(date_debut, '%Y-%m-%d').date()
    date_fin_obj = datetime.strptime(date_fin, '%Y-%m-%d').date()
    
    # Présences sur la période
    presences = Presence.query.filter(
        Presence.date >= date_debut_obj,
        Presence.date <= date_fin_obj
    ).all()
    
    # Statistiques
    personnel_actif = Personnel.query.filter_by(statut='Actif').count()
    jours_travailles = len(set(p.date for p in presences))
    total_heures = sum(p.heures_travaillees or 0 for p in presences)
    taux_presence = len([p for p in presences if p.statut == 'Présent']) / len(presences) * 100 if presences else 0
    
    # Présences par employé
    presences_par_employe = {}
    for presence in presences:
        emp_id = presence.personnel_id
        if emp_id not in presences_par_employe:
            presences_par_employe[emp_id] = {'present': 0, 'absent': 0, 'retard': 0, 'heures': 0}
        
        if presence.statut == 'Présent':
            presences_par_employe[emp_id]['present'] += 1
        elif presence.statut == 'Absent':
            presences_par_employe[emp_id]['absent'] += 1
        elif presence.statut == 'Retard':
            presences_par_employe[emp_id]['retard'] += 1
        
        presences_par_employe[emp_id]['heures'] += presence.heures_travaillees or 0
    
    return render_template('rapports/personnel.html',
                         personnel_actif=personnel_actif,
                         jours_travailles=jours_travailles,
                         total_heures=total_heures,
                         taux_presence=taux_presence,
                         presences_par_employe=presences_par_employe,
                         date_debut=date_debut,
                         date_fin=date_fin)

@rapports_bp.route('/stocks')
@login_required
def rapport_stocks():
    """Rapport stocks"""
    stocks = Stock.query.all()
    
    # Statistiques
    valeur_totale = sum(s.valeur_stock or 0 for s in stocks)
    stocks_critiques = [s for s in stocks if s.niveau_critique]
    stocks_reappro = [s for s in stocks if s.besoin_reapprovisionnement]
    
    # Mouvements récents
    mouvements_recents = MouvementStock.query.order_by(
        MouvementStock.date_mouvement.desc()
    ).limit(50).all()
    
    return render_template('rapports/stocks.html',
                         stocks=stocks,
                         valeur_totale=valeur_totale,
                         stocks_critiques=stocks_critiques,
                         stocks_reappro=stocks_reappro,
                         mouvements_recents=mouvements_recents)

@rapports_bp.route('/qualite')
@login_required
def rapport_qualite():
    """Rapport qualité"""
    # Paramètres de date
    date_debut = request.args.get('date_debut', (date.today() - timedelta(days=30)).strftime('%Y-%m-%d'))
    date_fin = request.args.get('date_fin', date.today().strftime('%Y-%m-%d'))
    
    # Convertir les dates
    date_debut_obj = datetime.strptime(date_debut, '%Y-%m-%d').date()
    date_fin_obj = datetime.strptime(date_fin, '%Y-%m-%d').date()
    
    # Contrôles sur la période
    controles = ControleQualite.query.filter(
        ControleQualite.date_prelevement >= date_debut_obj,
        ControleQualite.date_prelevement <= date_fin_obj,
        ControleQualite.conforme.isnot(None)
    ).all()
    
    # Statistiques
    nb_controles = len(controles)
    nb_conformes = len([c for c in controles if c.conforme])
    nb_non_conformes = nb_controles - nb_conformes
    taux_conformite = (nb_conformes / nb_controles * 100) if nb_controles > 0 else 0
    
    # Graphique conformité
    if controles:
        fig = go.Figure(data=[go.Pie(labels=['Conforme', 'Non conforme'], values=[nb_conformes, nb_non_conformes])])
        fig.update_layout(title='Répartition de la conformité')
        graphJSON = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
    else:
        graphJSON = None
    
    return render_template('rapports/qualite.html',
                         controles=controles,
                         nb_controles=nb_controles,
                         nb_conformes=nb_conformes,
                         nb_non_conformes=nb_non_conformes,
                         taux_conformite=taux_conformite,
                         graphJSON=graphJSON,
                         date_debut=date_debut,
                         date_fin=date_fin)

@rapports_bp.route('/export_excel/<string:type_rapport>')
@login_required
def export_excel(type_rapport):
    """Export des rapports en Excel - Temporairement désactivé"""
    flash('Export Excel temporairement désactivé. Installez pandas et openpyxl pour activer cette fonctionnalité.', 'warning')
    return redirect(url_for('rapports.index'))
