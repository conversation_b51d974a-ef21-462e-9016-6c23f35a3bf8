from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.maintenance import Maintenance, PlanMaintenance, PieceRechange
from models.production import Equipement
from models.personnel import Personnel
from database import db
from datetime import datetime, date, timedelta

maintenance_bp = Blueprint('maintenance', __name__)

@maintenance_bp.route('/')
@login_required
def index():
    """Vue d'ensemble de la maintenance"""
    # Maintenances en cours
    maintenances_en_cours = Maintenance.query.filter_by(statut='En cours').all()
    
    # Maintenances planifiées pour cette semaine
    today = date.today()
    week_end = today + timedelta(days=7)
    maintenances_semaine = Maintenance.query.filter(
        Maintenance.date_planifiee >= today,
        Maintenance.date_planifiee <= week_end,
        Maintenance.statut == 'Planifiée'
    ).all()
    
    # Équipements nécessitant une maintenance
    equipements_maintenance = Equipement.query.filter(
        Equipement.prochaine_maintenance <= today
    ).all()
    
    # Pièces en rupture de stock
    pieces_rupture = PieceRechange.query.filter(
        PieceRechange.stock_actuel <= PieceRechange.stock_minimum
    ).all()
    
    return render_template('maintenance/index.html',
                         maintenances_en_cours=maintenances_en_cours,
                         maintenances_semaine=maintenances_semaine,
                         equipements_maintenance=equipements_maintenance,
                         pieces_rupture=pieces_rupture)

@maintenance_bp.route('/interventions')
@login_required
def interventions():
    """Liste des interventions de maintenance"""
    page = request.args.get('page', 1, type=int)
    type_filter = request.args.get('type', '')
    statut_filter = request.args.get('statut', '')
    
    query = Maintenance.query
    
    if type_filter:
        query = query.filter_by(type_maintenance=type_filter)
    if statut_filter:
        query = query.filter_by(statut=statut_filter)
    
    maintenances = query.order_by(Maintenance.date_demande.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('maintenance/interventions.html', 
                         maintenances=maintenances,
                         type_filter=type_filter,
                         statut_filter=statut_filter)

@maintenance_bp.route('/nouvelle_intervention', methods=['GET', 'POST'])
@login_required
def nouvelle_intervention():
    """Création d'une nouvelle intervention"""
    if request.method == 'POST':
        try:
            maintenance = Maintenance(
                equipement_id=request.form['equipement_id'],
                type_maintenance=request.form['type_maintenance'],
                priorite=request.form['priorite'],
                description=request.form['description'],
                date_planifiee=datetime.strptime(request.form['date_planifiee'], '%Y-%m-%dT%H:%M') if request.form['date_planifiee'] else None,
                duree_prevue=int(request.form['duree_prevue']) if request.form['duree_prevue'] else None,
                technicien_id=request.form['technicien_id'] if request.form['technicien_id'] else None,
                demandeur_id=current_user.id
            )
            
            db.session.add(maintenance)
            db.session.commit()
            flash('Intervention créée avec succès', 'success')
            return redirect(url_for('maintenance.interventions'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la création: {str(e)}', 'error')
    
    equipements = Equipement.query.all()
    techniciens = Personnel.query.filter_by(statut='Actif').all()
    
    return render_template('maintenance/nouvelle_intervention.html',
                         equipements=equipements,
                         techniciens=techniciens)

@maintenance_bp.route('/intervention/<int:maintenance_id>')
@login_required
def detail_intervention(maintenance_id):
    """Détail d'une intervention"""
    maintenance = Maintenance.query.get_or_404(maintenance_id)
    return render_template('maintenance/detail_intervention.html', 
                         maintenance=maintenance)

@maintenance_bp.route('/intervention/<int:maintenance_id>/modifier', methods=['GET', 'POST'])
@login_required
def modifier_intervention(maintenance_id):
    """Modification d'une intervention"""
    maintenance = Maintenance.query.get_or_404(maintenance_id)
    
    if request.method == 'POST':
        try:
            maintenance.statut = request.form['statut']
            maintenance.diagnostic = request.form['diagnostic']
            maintenance.travaux_realises = request.form['travaux_realises']
            maintenance.pieces_utilisees = request.form['pieces_utilisees']
            maintenance.cout_main_oeuvre = float(request.form['cout_main_oeuvre']) if request.form['cout_main_oeuvre'] else 0
            maintenance.cout_pieces = float(request.form['cout_pieces']) if request.form['cout_pieces'] else 0
            maintenance.cout_total = maintenance.cout_main_oeuvre + maintenance.cout_pieces
            maintenance.commentaires = request.form['commentaires']
            
            if request.form['date_debut']:
                maintenance.date_debut = datetime.strptime(request.form['date_debut'], '%Y-%m-%dT%H:%M')
            if request.form['date_fin']:
                maintenance.date_fin = datetime.strptime(request.form['date_fin'], '%Y-%m-%dT%H:%M')
                
            db.session.commit()
            flash('Intervention modifiée avec succès', 'success')
            return redirect(url_for('maintenance.detail_intervention', maintenance_id=maintenance_id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la modification: {str(e)}', 'error')
    
    return render_template('maintenance/modifier_intervention.html', 
                         maintenance=maintenance)

@maintenance_bp.route('/preventive')
@login_required
def preventive():
    """Maintenance préventive"""
    plans = PlanMaintenance.query.filter_by(actif=True).all()
    
    # Plans en retard
    today = date.today()
    plans_retard = [p for p in plans if p.prochaine_execution and p.prochaine_execution < today]
    
    return render_template('maintenance/preventive.html', 
                         plans=plans,
                         plans_retard=plans_retard)

@maintenance_bp.route('/nouveau_plan_preventif', methods=['GET', 'POST'])
@login_required
def nouveau_plan_preventif():
    """Création d'un plan de maintenance préventive"""
    if request.method == 'POST':
        try:
            plan = PlanMaintenance(
                equipement_id=request.form['equipement_id'],
                nom_plan=request.form['nom_plan'],
                description=request.form['description'],
                frequence_type=request.form['frequence_type'],
                frequence_valeur=int(request.form['frequence_valeur']),
                duree_estimee=int(request.form['duree_estimee']) if request.form['duree_estimee'] else None,
                pieces_necessaires=request.form['pieces_necessaires'],
                instructions=request.form['instructions']
            )
            
            if request.form['derniere_execution']:
                plan.derniere_execution = datetime.strptime(request.form['derniere_execution'], '%Y-%m-%d').date()
                plan.prochaine_execution = plan.calculer_prochaine_execution()
            
            db.session.add(plan)
            db.session.commit()
            flash('Plan de maintenance préventive créé avec succès', 'success')
            return redirect(url_for('maintenance.preventive'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la création: {str(e)}', 'error')
    
    equipements = Equipement.query.all()
    return render_template('maintenance/nouveau_plan_preventif.html', 
                         equipements=equipements)

@maintenance_bp.route('/pieces_rechange')
@login_required
def pieces_rechange():
    """Gestion des pièces de rechange"""
    pieces = PieceRechange.query.all()
    
    # Pièces nécessitant un réapprovisionnement
    pieces_reappro = [p for p in pieces if p.besoin_reapprovisionnement]
    
    return render_template('maintenance/pieces_rechange.html', 
                         pieces=pieces,
                         pieces_reappro=pieces_reappro)

@maintenance_bp.route('/api/maintenance_stats')
@login_required
def api_maintenance_stats():
    """API pour les statistiques de maintenance"""
    # Statistiques des 12 derniers mois
    today = date.today()
    start_date = today.replace(day=1) - timedelta(days=365)
    
    maintenances = Maintenance.query.filter(
        Maintenance.date_demande >= start_date
    ).all()
    
    # Grouper par mois
    stats_mensuelles = {}
    for maintenance in maintenances:
        mois = maintenance.date_demande.strftime('%Y-%m')
        if mois not in stats_mensuelles:
            stats_mensuelles[mois] = {'corrective': 0, 'preventive': 0, 'cout': 0}
        
        if maintenance.type_maintenance == 'Corrective':
            stats_mensuelles[mois]['corrective'] += 1
        else:
            stats_mensuelles[mois]['preventive'] += 1
        
        stats_mensuelles[mois]['cout'] += maintenance.cout_total or 0
    
    return jsonify(stats_mensuelles)
