from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.production import UniteProduction, ProductionData, Equipement, PlanProduction
from models.personnel import Personnel
from database import db
from datetime import datetime, date
import json

production_bp = Blueprint('production', __name__)

@production_bp.route('/')
@login_required
def index():
    """Vue d'ensemble de la production"""
    unites = UniteProduction.query.all()
    today = date.today()
    
    # Production du jour par unité
    production_today = {}
    for unite in unites:
        prod_data = ProductionData.query.filter_by(
            unite_id=unite.id, 
            date=today
        ).first()
        production_today[unite.id] = prod_data
    
    return render_template('production/index.html', 
                         unites=unites, 
                         production_today=production_today)

@production_bp.route('/unites')
@login_required
def unites():
    """Gestion des unités de production"""
    unites = UniteProduction.query.all()
    return render_template('production/unites.html', unites=unites)

@production_bp.route('/unite/<int:unite_id>')
@login_required
def detail_unite(unite_id):
    """Détail d'une unité de production"""
    unite = UniteProduction.query.get_or_404(unite_id)
    
    # Production des 7 derniers jours
    from datetime import timedelta
    end_date = date.today()
    start_date = end_date - timedelta(days=7)
    
    productions = ProductionData.query.filter(
        ProductionData.unite_id == unite_id,
        ProductionData.date >= start_date,
        ProductionData.date <= end_date
    ).order_by(ProductionData.date.desc()).all()
    
    # Équipements de l'unité
    equipements = Equipement.query.filter_by(unite_id=unite_id).all()
    
    return render_template('production/detail_unite.html', 
                         unite=unite, 
                         productions=productions,
                         equipements=equipements)

@production_bp.route('/saisie_production', methods=['GET', 'POST'])
@login_required
def saisie_production():
    """Saisie des données de production"""
    if request.method == 'POST':
        try:
            production = ProductionData(
                unite_id=request.form['unite_id'],
                date=datetime.strptime(request.form['date'], '%Y-%m-%d').date(),
                heure_debut=datetime.strptime(request.form['heure_debut'], '%H:%M').time() if request.form['heure_debut'] else None,
                heure_fin=datetime.strptime(request.form['heure_fin'], '%H:%M').time() if request.form['heure_fin'] else None,
                quantite_produite=float(request.form['quantite_produite']) if request.form['quantite_produite'] else 0,
                quantite_planifiee=float(request.form['quantite_planifiee']) if request.form['quantite_planifiee'] else 0,
                rendement=float(request.form['rendement']) if request.form['rendement'] else 0,
                qualite_moyenne=float(request.form['qualite_moyenne']) if request.form['qualite_moyenne'] else 0,
                temps_arret=int(request.form['temps_arret']) if request.form['temps_arret'] else 0,
                raison_arret=request.form['raison_arret'],
                operateur_id=request.form['operateur_id'] if request.form['operateur_id'] else None,
                commentaires=request.form['commentaires']
            )
            
            db.session.add(production)
            db.session.commit()
            flash('Données de production enregistrées avec succès', 'success')
            return redirect(url_for('production.index'))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de l\'enregistrement: {str(e)}', 'error')
    
    # Données pour le formulaire
    unites = UniteProduction.query.all()
    operateurs = Personnel.query.filter_by(statut='Actif').all()
    
    return render_template('production/saisie_production.html', 
                         unites=unites, 
                         operateurs=operateurs)

@production_bp.route('/planning')
@login_required
def planning():
    """Planning de production"""
    plans = PlanProduction.query.order_by(PlanProduction.date_debut.desc()).all()
    return render_template('production/planning.html', plans=plans)

@production_bp.route('/nouveau_plan', methods=['GET', 'POST'])
@login_required
def nouveau_plan():
    """Création d'un nouveau plan de production"""
    if request.method == 'POST':
        try:
            plan = PlanProduction(
                unite_id=request.form['unite_id'],
                date_debut=datetime.strptime(request.form['date_debut'], '%Y-%m-%dT%H:%M'),
                date_fin=datetime.strptime(request.form['date_fin'], '%Y-%m-%dT%H:%M'),
                objectif_quantite=float(request.form['objectif_quantite']) if request.form['objectif_quantite'] else 0,
                priorite=request.form['priorite'],
                responsable_id=request.form['responsable_id'] if request.form['responsable_id'] else None,
                commentaires=request.form['commentaires']
            )
            
            db.session.add(plan)
            db.session.commit()
            flash('Plan de production créé avec succès', 'success')
            return redirect(url_for('production.planning'))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la création: {str(e)}', 'error')
    
    unites = UniteProduction.query.all()
    responsables = Personnel.query.filter_by(statut='Actif').all()
    
    return render_template('production/nouveau_plan.html', 
                         unites=unites, 
                         responsables=responsables)

@production_bp.route('/api/production_data/<int:unite_id>')
@login_required
def api_production_data(unite_id):
    """API pour les données de production (graphiques)"""
    from datetime import timedelta
    
    days = request.args.get('days', 30, type=int)
    end_date = date.today()
    start_date = end_date - timedelta(days=days)
    
    productions = ProductionData.query.filter(
        ProductionData.unite_id == unite_id,
        ProductionData.date >= start_date,
        ProductionData.date <= end_date
    ).order_by(ProductionData.date).all()
    
    data = {
        'dates': [p.date.strftime('%Y-%m-%d') for p in productions],
        'quantites': [float(p.quantite_produite or 0) for p in productions],
        'rendements': [float(p.rendement or 0) for p in productions],
        'qualite': [float(p.qualite_moyenne or 0) for p in productions]
    }
    
    return jsonify(data)
