{% extends "base.html" %}

{% block title %}Changer le mot de passe - Gestion Unité Phosphate{% endblock %}
{% block page_title %}Changer le mot de passe{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-key"></i>
                    Modification du mot de passe
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Mot de passe actuel *</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="current_password" 
                                   name="current_password" required>
                            <button class="btn btn-outline-secondary" type="button" 
                                    onclick="togglePassword('current_password')">
                                <i class="fas fa-eye" id="current_password_icon"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">Nouveau mot de passe *</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-key"></i>
                            </span>
                            <input type="password" class="form-control" id="new_password" 
                                   name="new_password" required minlength="6">
                            <button class="btn btn-outline-secondary" type="button" 
                                    onclick="togglePassword('new_password')">
                                <i class="fas fa-eye" id="new_password_icon"></i>
                            </button>
                        </div>
                        <div class="form-text">Le mot de passe doit contenir au moins 6 caractères.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirmer le nouveau mot de passe *</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-check"></i>
                            </span>
                            <input type="password" class="form-control" id="confirm_password" 
                                   name="confirm_password" required minlength="6">
                            <button class="btn btn-outline-secondary" type="button" 
                                    onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye" id="confirm_password_icon"></i>
                            </button>
                        </div>
                        <div id="password_match_message" class="form-text"></div>
                    </div>
                    
                    <!-- Indicateur de force du mot de passe -->
                    <div class="mb-3">
                        <label class="form-label">Force du mot de passe</label>
                        <div class="progress" style="height: 8px;">
                            <div id="password_strength_bar" class="progress-bar" role="progressbar" 
                                 style="width: 0%"></div>
                        </div>
                        <small id="password_strength_text" class="form-text"></small>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Conseils pour un mot de passe sécurisé :</strong>
                        <ul class="mb-0 mt-2">
                            <li>Utilisez au moins 8 caractères</li>
                            <li>Mélangez majuscules et minuscules</li>
                            <li>Incluez des chiffres et des caractères spéciaux</li>
                            <li>Évitez les mots du dictionnaire</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour au profil
                        </a>
                        <button type="submit" class="btn btn-primary" id="submit_btn" disabled>
                            <i class="fas fa-save"></i> Changer le mot de passe
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Fonction pour afficher/masquer le mot de passe
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Vérification de la correspondance des mots de passe
function checkPasswordMatch() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const messageDiv = document.getElementById('password_match_message');
    const submitBtn = document.getElementById('submit_btn');
    
    if (confirmPassword === '') {
        messageDiv.textContent = '';
        messageDiv.className = 'form-text';
        submitBtn.disabled = true;
        return;
    }
    
    if (newPassword === confirmPassword) {
        messageDiv.textContent = '✓ Les mots de passe correspondent';
        messageDiv.className = 'form-text text-success';
        submitBtn.disabled = false;
    } else {
        messageDiv.textContent = '✗ Les mots de passe ne correspondent pas';
        messageDiv.className = 'form-text text-danger';
        submitBtn.disabled = true;
    }
}

// Évaluation de la force du mot de passe
function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = [];
    
    // Longueur
    if (password.length >= 8) strength += 1;
    else feedback.push('au moins 8 caractères');
    
    // Minuscules
    if (/[a-z]/.test(password)) strength += 1;
    else feedback.push('des minuscules');
    
    // Majuscules
    if (/[A-Z]/.test(password)) strength += 1;
    else feedback.push('des majuscules');
    
    // Chiffres
    if (/[0-9]/.test(password)) strength += 1;
    else feedback.push('des chiffres');
    
    // Caractères spéciaux
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;
    else feedback.push('des caractères spéciaux');
    
    return { strength, feedback };
}

function updatePasswordStrength() {
    const password = document.getElementById('new_password').value;
    const strengthBar = document.getElementById('password_strength_bar');
    const strengthText = document.getElementById('password_strength_text');
    
    if (password === '') {
        strengthBar.style.width = '0%';
        strengthBar.className = 'progress-bar';
        strengthText.textContent = '';
        return;
    }
    
    const { strength, feedback } = checkPasswordStrength(password);
    const percentage = (strength / 5) * 100;
    
    strengthBar.style.width = percentage + '%';
    
    if (strength <= 2) {
        strengthBar.className = 'progress-bar bg-danger';
        strengthText.textContent = 'Faible - Ajoutez: ' + feedback.slice(0, 2).join(', ');
        strengthText.className = 'form-text text-danger';
    } else if (strength <= 3) {
        strengthBar.className = 'progress-bar bg-warning';
        strengthText.textContent = 'Moyen - Ajoutez: ' + feedback.join(', ');
        strengthText.className = 'form-text text-warning';
    } else if (strength <= 4) {
        strengthBar.className = 'progress-bar bg-info';
        strengthText.textContent = 'Bon - Ajoutez: ' + feedback.join(', ');
        strengthText.className = 'form-text text-info';
    } else {
        strengthBar.className = 'progress-bar bg-success';
        strengthText.textContent = 'Excellent - Mot de passe sécurisé';
        strengthText.className = 'form-text text-success';
    }
}

// Event listeners
document.getElementById('new_password').addEventListener('input', function() {
    updatePasswordStrength();
    checkPasswordMatch();
});

document.getElementById('confirm_password').addEventListener('input', checkPasswordMatch);

// Validation du formulaire
document.querySelector('form').addEventListener('submit', function(e) {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (newPassword !== confirmPassword) {
        e.preventDefault();
        alert('Les mots de passe ne correspondent pas.');
        return false;
    }
    
    if (newPassword.length < 6) {
        e.preventDefault();
        alert('Le mot de passe doit contenir au moins 6 caractères.');
        return false;
    }
});
</script>
{% endblock %}
