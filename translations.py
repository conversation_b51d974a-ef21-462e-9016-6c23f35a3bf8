"""
Dictionnaire de traductions pour l'application Phosphate Manager
Centralise tous les termes utilisés dans l'interface
"""

# Statuts généraux
STATUTS = {
    'actif': 'Actif',
    'inactif': 'Inactif',
    'en_cours': 'En cours',
    'termine': 'Terminé',
    'planifie': 'Planifié',
    'annule': 'Annulé'
}

# Production
PRODUCTION = {
    'statuts': {
        'en_marche': 'En marche',
        'arretee': 'Arrêtée',
        'maintenance': 'En maintenance'
    },
    'unites': {
        'concassage': 'Concassage',
        'lavage': 'Lavage',
        'systeme_eau': 'Système d\'eau'
    },
    'equipements': {
        'operationnel': 'Opérationnel',
        'en_panne': 'En panne',
        'maintenance': 'En maintenance'
    }
}

# Maintenance
MAINTENANCE = {
    'types': {
        'corrective': 'Corrective',
        'preventive': 'Préventive'
    },
    'priorites': {
        'urgente': 'Urgente',
        'haute': 'Haute',
        'normale': 'Normale',
        'basse': 'Basse'
    },
    'statuts': {
        'planifiee': 'Planifiée',
        'en_cours': 'En cours',
        'terminee': 'Terminée',
        'annulee': 'Annulée'
    },
    'frequences': {
        'heures': 'Heures',
        'jours': 'Jours',
        'semaines': 'Semaines',
        'mois': 'Mois'
    }
}

# Personnel
PERSONNEL = {
    'statuts': {
        'actif': 'Actif',
        'conge': 'En congé',
        'arret_maladie': 'Arrêt maladie',
        'demission': 'Démission'
    },
    'presences': {
        'present': 'Présent',
        'absent': 'Absent',
        'retard': 'En retard',
        'conge': 'En congé'
    },
    'niveaux_competence': {
        'debutant': 'Débutant',
        'intermediaire': 'Intermédiaire',
        'expert': 'Expert'
    },
    'types_service': {
        'jour': 'Jour',
        'nuit': 'Nuit',
        'weekend': 'Weekend'
    },
    'types_conge': {
        'annuel': 'Congé annuel',
        'maladie': 'Congé maladie',
        'maternite': 'Congé maternité',
        'paternite': 'Congé paternité',
        'formation': 'Formation'
    },
    'statuts_conge': {
        'en_attente': 'En attente',
        'approuve': 'Approuvé',
        'refuse': 'Refusé'
    },
    'categories_competence': {
        'technique': 'Technique',
        'securite': 'Sécurité',
        'management': 'Management'
    }
}

# Stocks
STOCKS = {
    'statuts_livraison': {
        'commandee': 'Commandée',
        'en_transit': 'En transit',
        'livree': 'Livrée',
        'annulee': 'Annulée'
    },
    'types_mouvement': {
        'entree': 'Entrée',
        'sortie': 'Sortie',
        'ajustement': 'Ajustement'
    },
    'unites_mesure': {
        'tonnes': 'Tonnes',
        'm3': 'm³',
        'litres': 'Litres',
        'kg': 'kg'
    }
}

# Qualité
QUALITE = {
    'types_controle': {
        'matiere_premiere': 'Matière première',
        'produit_fini': 'Produit fini',
        'en_cours': 'En cours de production'
    },
    'statuts_controle': {
        'en_cours': 'En cours',
        'conforme': 'Conforme',
        'non_conforme': 'Non conforme'
    },
    'priorites_action': {
        'urgente': 'Urgente',
        'haute': 'Haute',
        'normale': 'Normale',
        'basse': 'Basse'
    },
    'statuts_action': {
        'ouverte': 'Ouverte',
        'en_cours': 'En cours',
        'fermee': 'Fermée',
        'annulee': 'Annulée'
    },
    'statuts_certificat': {
        'valide': 'Valide',
        'expire': 'Expiré',
        'annule': 'Annulé'
    },
    'periodes': {
        'jour': 'Jour',
        'semaine': 'Semaine',
        'mois': 'Mois'
    }
}

# Messages d'interface
MESSAGES = {
    'success': {
        'enregistrement': 'Enregistrement effectué avec succès',
        'modification': 'Modification effectuée avec succès',
        'suppression': 'Suppression effectuée avec succès',
        'connexion': 'Connexion réussie',
        'deconnexion': 'Déconnexion effectuée avec succès'
    },
    'error': {
        'enregistrement': 'Erreur lors de l\'enregistrement',
        'modification': 'Erreur lors de la modification',
        'suppression': 'Erreur lors de la suppression',
        'connexion': 'Erreur de connexion',
        'donnees_invalides': 'Données invalides',
        'acces_refuse': 'Accès refusé'
    },
    'warning': {
        'stock_faible': 'Stock faible',
        'maintenance_due': 'Maintenance due',
        'non_conformite': 'Non-conformité détectée'
    },
    'info': {
        'aucune_donnee': 'Aucune donnée disponible',
        'en_cours_traitement': 'Traitement en cours',
        'mise_a_jour': 'Mise à jour disponible'
    }
}

# Libellés d'interface
LABELS = {
    'navigation': {
        'tableau_bord': 'Tableau de bord',
        'production': 'Production',
        'maintenance': 'Maintenance',
        'personnel': 'Personnel',
        'stocks': 'Stocks',
        'qualite': 'Qualité',
        'rapports': 'Rapports'
    },
    'actions': {
        'ajouter': 'Ajouter',
        'modifier': 'Modifier',
        'supprimer': 'Supprimer',
        'enregistrer': 'Enregistrer',
        'annuler': 'Annuler',
        'valider': 'Valider',
        'rechercher': 'Rechercher',
        'exporter': 'Exporter',
        'importer': 'Importer'
    },
    'champs': {
        'nom': 'Nom',
        'prenom': 'Prénom',
        'email': 'Email',
        'telephone': 'Téléphone',
        'adresse': 'Adresse',
        'date': 'Date',
        'heure': 'Heure',
        'quantite': 'Quantité',
        'prix': 'Prix',
        'description': 'Description',
        'commentaires': 'Commentaires',
        'statut': 'Statut'
    }
}

# Départements
DEPARTEMENTS = {
    'production': 'Production',
    'maintenance': 'Maintenance',
    'qualite': 'Qualité',
    'administration': 'Administration',
    'direction': 'Direction',
    'securite': 'Sécurité',
    'logistique': 'Logistique'
}

# Postes de travail
POSTES = {
    'superviseur': 'Superviseur',
    'operateur': 'Opérateur',
    'technicien': 'Technicien',
    'controleur': 'Contrôleur',
    'responsable': 'Responsable',
    'chef_equipe': 'Chef d\'équipe',
    'ingenieur': 'Ingénieur'
}

def get_translation(category, key, default=None):
    """
    Récupère une traduction depuis le dictionnaire
    
    Args:
        category (str): Catégorie de traduction
        key (str): Clé de traduction
        default (str): Valeur par défaut si non trouvée
    
    Returns:
        str: Traduction ou valeur par défaut
    """
    translations = globals().get(category.upper(), {})
    return translations.get(key, default or key)

def get_status_badge_class(status):
    """
    Retourne la classe CSS Bootstrap pour un statut donné
    
    Args:
        status (str): Statut à traduire
    
    Returns:
        str: Classe CSS Bootstrap
    """
    status_classes = {
        'actif': 'success',
        'en_cours': 'primary',
        'termine': 'success',
        'planifie': 'info',
        'annule': 'secondary',
        'urgent': 'danger',
        'haute': 'warning',
        'normale': 'secondary',
        'conforme': 'success',
        'non_conforme': 'danger',
        'present': 'success',
        'absent': 'danger',
        'retard': 'warning'
    }
    
    return status_classes.get(status.lower(), 'secondary')
