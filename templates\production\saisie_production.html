{% extends "base.html" %}

{% block title %}Saisie Production - Gestion Unité Phosphate{% endblock %}
{% block page_title %}Saisie des données de production{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit"></i>
                    Nouvelle saisie de production
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="unite_id" class="form-label">Unité de production *</label>
                                <select class="form-select" id="unite_id" name="unite_id" required>
                                    <option value="">Sélectionner une unité</option>
                                    {% for unite in unites %}
                                    <option value="{{ unite.id }}">{{ unite.nom }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date" class="form-label">Date *</label>
                                <input type="date" class="form-control" id="date" name="date" 
                                       value="{{ moment().format('YYYY-MM-DD') }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="heure_debut" class="form-label">Heure de début</label>
                                <input type="time" class="form-control" id="heure_debut" name="heure_debut">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="heure_fin" class="form-label">Heure de fin</label>
                                <input type="time" class="form-control" id="heure_fin" name="heure_fin">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quantite_produite" class="form-label">Quantité produite (tonnes) *</label>
                                <input type="number" step="0.1" class="form-control" id="quantite_produite" 
                                       name="quantite_produite" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quantite_planifiee" class="form-label">Quantité planifiée (tonnes)</label>
                                <input type="number" step="0.1" class="form-control" id="quantite_planifiee" 
                                       name="quantite_planifiee">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="rendement" class="form-label">Rendement (%)</label>
                                <input type="number" step="0.1" class="form-control" id="rendement" 
                                       name="rendement" min="0" max="100">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="qualite_moyenne" class="form-label">Qualité moyenne (%)</label>
                                <input type="number" step="0.1" class="form-control" id="qualite_moyenne" 
                                       name="qualite_moyenne" min="0" max="100">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="temps_arret" class="form-label">Temps d'arrêt (minutes)</label>
                                <input type="number" class="form-control" id="temps_arret" 
                                       name="temps_arret" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="operateur_id" class="form-label">Opérateur</label>
                                <select class="form-select" id="operateur_id" name="operateur_id">
                                    <option value="">Sélectionner un opérateur</option>
                                    {% for operateur in operateurs %}
                                    <option value="{{ operateur.id }}">{{ operateur.prenom }} {{ operateur.nom }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="raison_arret" class="form-label">Raison de l'arrêt</label>
                        <textarea class="form-control" id="raison_arret" name="raison_arret" rows="2"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="commentaires" class="form-label">Commentaires</label>
                        <textarea class="form-control" id="commentaires" name="commentaires" rows="3"></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('production.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Calcul automatique du rendement
document.getElementById('quantite_produite').addEventListener('input', calculateRendement);
document.getElementById('quantite_planifiee').addEventListener('input', calculateRendement);

function calculateRendement() {
    const produite = parseFloat(document.getElementById('quantite_produite').value) || 0;
    const planifiee = parseFloat(document.getElementById('quantite_planifiee').value) || 0;
    
    if (planifiee > 0) {
        const rendement = (produite / planifiee) * 100;
        document.getElementById('rendement').value = rendement.toFixed(1);
    }
}

// Définir la date d'aujourd'hui par défaut
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('date').value = today;
});
</script>
{% endblock %}
