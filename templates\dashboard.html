{% extends "base.html" %}

{% block title %}Tableau de bord - Gestion Unité Phosphate{% endblock %}
{% block page_title %}Tableau de bord{% endblock %}

{% block content %}
<div class="row">
    <!-- Cartes de statistiques -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Production du jour
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ production_today|length }} unités actives
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-cogs fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Personnel présent
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ personnel_present }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Maintenances actives
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ maintenances_actives|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-wrench fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Alertes qualité
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            0
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Production du jour -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Production du jour</h6>
                <a href="{{ url_for('production.index') }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-eye"></i> Voir tout
                </a>
            </div>
            <div class="card-body">
                {% if production_today %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Unité</th>
                                    <th>Quantité</th>
                                    <th>Rendement</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for prod in production_today %}
                                <tr>
                                    <td>{{ prod.unite.nom if prod.unite else 'N/A' }}</td>
                                    <td>{{ "%.1f"|format(prod.quantite_produite or 0) }} T</td>
                                    <td>{{ "%.1f"|format(prod.rendement or 0) }}%</td>
                                    <td>
                                        {% if prod.rendement and prod.rendement >= 80 %}
                                            <span class="badge bg-success">Bon</span>
                                        {% elif prod.rendement and prod.rendement >= 60 %}
                                            <span class="badge bg-warning">Moyen</span>
                                        {% else %}
                                            <span class="badge bg-danger">Faible</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">Aucune donnée de production pour aujourd'hui.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Maintenances en cours -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Maintenances en cours</h6>
                <a href="{{ url_for('maintenance.index') }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-eye"></i> Voir tout
                </a>
            </div>
            <div class="card-body">
                {% if maintenances_actives %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Équipement</th>
                                    <th>Type</th>
                                    <th>Priorité</th>
                                    <th>Technicien</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for maintenance in maintenances_actives %}
                                <tr>
                                    <td>{{ maintenance.equipement.nom if maintenance.equipement else 'N/A' }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'warning' if maintenance.type_maintenance == 'Corrective' else 'info' }}">
                                            {{ maintenance.type_maintenance }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if maintenance.priorite == 'Urgente' %}
                                            <span class="badge bg-danger">{{ maintenance.priorite }}</span>
                                        {% elif maintenance.priorite == 'Haute' %}
                                            <span class="badge bg-warning">{{ maintenance.priorite }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ maintenance.priorite }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ maintenance.technicien.nom if maintenance.technicien else 'Non assigné' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">Aucune maintenance en cours.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Graphique de production -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Évolution de la production (7 derniers jours)</h6>
            </div>
            <div class="card-body">
                <canvas id="productionChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Alertes et notifications -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Alertes et notifications</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <strong>Attention!</strong> Stock de matière première faible.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <strong>Info:</strong> Maintenance préventive programmée demain.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Graphique de production
const ctx = document.getElementById('productionChart').getContext('2d');
const productionChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
        datasets: [{
            label: 'Production (tonnes)',
            data: [120, 135, 110, 145, 130, 125, 140],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
{% endblock %}
