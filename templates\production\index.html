{% extends "base.html" %}

{% block title %}Production - Gestion Unité Phosphate{% endblock %}
{% block page_title %}Production{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4>Vue d'ensemble de la production</h4>
            <div>
                <a href="{{ url_for('production.saisie_production') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Saisir production
                </a>
                <a href="{{ url_for('production.planning') }}" class="btn btn-outline-primary">
                    <i class="fas fa-calendar"></i> Planning
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    {% for unite in unites %}
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-cogs"></i>
                    {{ unite.nom }}
                </h5>
                <span class="badge bg-{{ 'success' if unite.statut == 'En marche' else 'secondary' }}">
                    {{ unite.statut }}
                </span>
            </div>
            <div class="card-body">
                <p class="text-muted">{{ unite.description }}</p>
                
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">Capacité nominale</small>
                        <div class="h6">{{ unite.capacite_nominale }} T/h</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Production aujourd'hui</small>
                        <div class="h6">
                            {% if production_today[unite.id] %}
                                {{ "%.1f"|format(production_today[unite.id].quantite_produite or 0) }} T
                            {% else %}
                                0 T
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                {% if production_today[unite.id] %}
                <div class="mt-3">
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">Rendement</small>
                            <div class="text-{{ 'success' if production_today[unite.id].rendement and production_today[unite.id].rendement >= 80 else 'warning' if production_today[unite.id].rendement and production_today[unite.id].rendement >= 60 else 'danger' }}">
                                {{ "%.1f"|format(production_today[unite.id].rendement or 0) }}%
                            </div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Qualité</small>
                            <div class="text-{{ 'success' if production_today[unite.id].qualite_moyenne and production_today[unite.id].qualite_moyenne >= 90 else 'warning' if production_today[unite.id].qualite_moyenne and production_today[unite.id].qualite_moyenne >= 80 else 'danger' }}">
                                {{ "%.1f"|format(production_today[unite.id].qualite_moyenne or 0) }}%
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <div class="mt-3">
                    <a href="{{ url_for('production.detail_unite', unite_id=unite.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> Détails
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line"></i>
                    Production des 7 derniers jours
                </h5>
            </div>
            <div class="card-body">
                <canvas id="productionChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    Alertes production
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <small><strong>Attention:</strong> Rendement faible sur l'unité de lavage</small>
                </div>
                <div class="alert alert-info">
                    <small><strong>Info:</strong> Maintenance programmée demain sur le concasseur</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Graphique de production des 7 derniers jours
const ctx = document.getElementById('productionChart').getContext('2d');
const productionChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
        datasets: [
            {
                label: 'Concassage',
                data: [45, 52, 48, 61, 55, 49, 58],
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1
            },
            {
                label: 'Lavage',
                data: [38, 42, 35, 47, 41, 38, 44],
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                tension: 0.1
            },
            {
                label: 'Système d\'eau',
                data: [85, 92, 78, 95, 88, 82, 90],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Production (tonnes)'
                }
            }
        },
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: false
            }
        }
    }
});
</script>
{% endblock %}
