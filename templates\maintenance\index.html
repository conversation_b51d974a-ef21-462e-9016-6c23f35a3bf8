{% extends "base.html" %}

{% block title %}Maintenance - Gestion Unité Phosphate{% endblock %}
{% block page_title %}Maintenance{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4>Vue d'ensemble de la maintenance</h4>
            <div>
                <a href="{{ url_for('maintenance.nouvelle_intervention') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Nouvelle intervention
                </a>
                <a href="{{ url_for('maintenance.preventive') }}" class="btn btn-outline-success">
                    <i class="fas fa-calendar-check"></i> Maintenance préventive
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Cartes de statistiques -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Interventions en cours
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ maintenances_en_cours|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tools fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Planifiées cette semaine
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ maintenances_semaine|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Équipements à maintenir
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ equipements_maintenance|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-secondary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                            Pièces en rupture
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ pieces_rupture|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box-open fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Interventions en cours -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Interventions en cours</h6>
                <a href="{{ url_for('maintenance.interventions') }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-eye"></i> Voir toutes
                </a>
            </div>
            <div class="card-body">
                {% if maintenances_en_cours %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Équipement</th>
                                    <th>Type</th>
                                    <th>Priorité</th>
                                    <th>Technicien</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for maintenance in maintenances_en_cours %}
                                <tr>
                                    <td>{{ maintenance.equipement.nom if maintenance.equipement else 'N/A' }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'warning' if maintenance.type_maintenance == 'Corrective' else 'info' }}">
                                            {{ maintenance.type_maintenance }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if maintenance.priorite == 'Urgente' %}
                                            <span class="badge bg-danger">{{ maintenance.priorite }}</span>
                                        {% elif maintenance.priorite == 'Haute' %}
                                            <span class="badge bg-warning">{{ maintenance.priorite }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ maintenance.priorite }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ maintenance.technicien.nom if maintenance.technicien else 'Non assigné' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">Aucune intervention en cours.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Planifiées cette semaine -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Planifiées cette semaine</h6>
            </div>
            <div class="card-body">
                {% if maintenances_semaine %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Équipement</th>
                                    <th>Type</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for maintenance in maintenances_semaine %}
                                <tr>
                                    <td>{{ maintenance.date_planifiee.strftime('%d/%m') if maintenance.date_planifiee else 'N/A' }}</td>
                                    <td>{{ maintenance.equipement.nom if maintenance.equipement else 'N/A' }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'warning' if maintenance.type_maintenance == 'Corrective' else 'info' }}">
                                            {{ maintenance.type_maintenance }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ maintenance.statut }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">Aucune maintenance planifiée cette semaine.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Équipements nécessitant une maintenance -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-danger">Équipements nécessitant une maintenance</h6>
            </div>
            <div class="card-body">
                {% if equipements_maintenance %}
                    <div class="list-group">
                        {% for equipement in equipements_maintenance %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ equipement.nom }}</h6>
                                <small class="text-muted">{{ equipement.type_equipement }}</small>
                            </div>
                            <div>
                                <small class="text-danger">
                                    Maintenance prévue le {{ equipement.prochaine_maintenance.strftime('%d/%m/%Y') if equipement.prochaine_maintenance else 'N/A' }}
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">Tous les équipements sont à jour.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Pièces en rupture de stock -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-warning">Pièces en rupture de stock</h6>
                <a href="{{ url_for('maintenance.pieces_rechange') }}" class="btn btn-sm btn-warning">
                    <i class="fas fa-eye"></i> Voir toutes
                </a>
            </div>
            <div class="card-body">
                {% if pieces_rupture %}
                    <div class="list-group">
                        {% for piece in pieces_rupture %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ piece.nom }}</h6>
                                <small class="text-muted">Réf: {{ piece.reference }}</small>
                            </div>
                            <div>
                                <span class="badge bg-danger">
                                    {{ piece.stock_actuel }}/{{ piece.stock_minimum }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">Toutes les pièces sont en stock suffisant.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
