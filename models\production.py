from datetime import datetime
from database import db

class UniteProduction(db.Model):
    """Modèle pour les unités de production"""
    __tablename__ = 'unites_production'
    
    id = db.Column(db.Integer, primary_key=True)
    nom = db.Column(db.String(100), nullable=False)  # Concassage, Lavage, Système d'eau
    description = db.Column(db.Text)
    capacite_nominale = db.Column(db.Float)  # Tonnes/heure
    statut = db.Column(db.String(50), default='Arrêtée')  # En marche, Arrêtée, Maintenance
    date_creation = db.Column(db.DateTime, default=datetime.utcnow)

class ProductionData(db.Model):
    """Données de production quotidiennes"""
    __tablename__ = 'production_data'
    
    id = db.Column(db.Integer, primary_key=True)
    unite_id = db.Column(db.Integer, db.<PERSON>ey('unites_production.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    heure_debut = db.Column(db.Time)
    heure_fin = db.Column(db.Time)
    quantite_produite = db.Column(db.Float)  # Tonnes
    quantite_planifiee = db.Column(db.Float)  # Tonnes
    rendement = db.Column(db.Float)  # %
    qualite_moyenne = db.Column(db.Float)  # %
    temps_arret = db.Column(db.Integer)  # Minutes
    raison_arret = db.Column(db.Text)
    operateur_id = db.Column(db.Integer, db.ForeignKey('personnel.id'))
    commentaires = db.Column(db.Text)
    date_creation = db.Column(db.DateTime, default=datetime.utcnow)
    
    @property
    def taux_realisation(self):
        """Calcul du taux de réalisation"""
        if self.quantite_planifiee and self.quantite_planifiee > 0:
            return (self.quantite_produite / self.quantite_planifiee) * 100
        return 0

class Equipement(db.Model):
    """Équipements des unités de production"""
    __tablename__ = 'equipements'
    
    id = db.Column(db.Integer, primary_key=True)
    nom = db.Column(db.String(100), nullable=False)
    type_equipement = db.Column(db.String(100))  # Concasseur, Pompe, Moteur, etc.
    numero_serie = db.Column(db.String(100))
    unite_id = db.Column(db.Integer, db.ForeignKey('unites_production.id'))
    statut = db.Column(db.String(50), default='Opérationnel')
    date_installation = db.Column(db.Date)
    date_derniere_maintenance = db.Column(db.Date)
    prochaine_maintenance = db.Column(db.Date)
    specifications = db.Column(db.Text)

class PlanProduction(db.Model):
    """Planning de production"""
    __tablename__ = 'plan_production'
    
    id = db.Column(db.Integer, primary_key=True)
    unite_id = db.Column(db.Integer, db.ForeignKey('unites_production.id'), nullable=False)
    date_debut = db.Column(db.DateTime, nullable=False)
    date_fin = db.Column(db.DateTime, nullable=False)
    objectif_quantite = db.Column(db.Float)  # Tonnes
    priorite = db.Column(db.String(20), default='Normale')  # Haute, Normale, Basse
    statut = db.Column(db.String(50), default='Planifié')  # Planifié, En cours, Terminé, Annulé
    responsable_id = db.Column(db.Integer, db.ForeignKey('personnel.id'))
    commentaires = db.Column(db.Text)
    date_creation = db.Column(db.DateTime, default=datetime.utcnow)
