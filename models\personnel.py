from datetime import datetime, date, timedelta
from database import db

class Personnel(db.Model):
    """Modèle pour le personnel"""
    __tablename__ = 'personnel'
    
    id = db.Column(db.Integer, primary_key=True)
    matricule = db.Column(db.String(20), unique=True, nullable=False)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    
    # Informations personnelles
    date_naissance = db.Column(db.Date)
    lieu_naissance = db.Column(db.String(100))
    adresse = db.Column(db.Text)
    telephone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    
    # Informations professionnelles
    poste = db.Column(db.String(100), nullable=False)
    departement = db.Column(db.String(100))
    niveau_competence = db.Column(db.String(50))  # Débutant, Intermédiaire, Expert
    date_embauche = db.Column(db.Date, nullable=False)
    salaire = db.Column(db.Float)
    
    # Statut
    statut = db.Column(db.String(50), default='Actif')  # Actif, Congé, Arrêt maladie, Démission
    present = db.Column(db.Boolean, default=False)
    
    # Sécurité
    formations_securite = db.Column(db.Text)  # JSON des formations
    date_derniere_visite_medicale = db.Column(db.Date)
    
    date_creation = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    presences = db.relationship('Presence', backref='employe', lazy=True, foreign_keys='Presence.personnel_id')
    conges = db.relationship('Conge', backref='employe', lazy=True, foreign_keys='Conge.personnel_id')
    competences = db.relationship('CompetencePersonnel', backref='employe', lazy=True)

class Presence(db.Model):
    """Suivi de présence du personnel"""
    __tablename__ = 'presences'
    
    id = db.Column(db.Integer, primary_key=True)
    personnel_id = db.Column(db.Integer, db.ForeignKey('personnel.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    heure_arrivee = db.Column(db.Time)
    heure_depart = db.Column(db.Time)
    heures_travaillees = db.Column(db.Float)
    heures_supplementaires = db.Column(db.Float, default=0)
    statut = db.Column(db.String(50), default='Présent')  # Présent, Absent, Retard, Congé
    commentaires = db.Column(db.Text)
    
    @property
    def duree_travail(self):
        """Calcule la durée de travail en heures"""
        if self.heure_arrivee and self.heure_depart:
            debut = datetime.combine(date.today(), self.heure_arrivee)
            fin = datetime.combine(date.today(), self.heure_depart)
            if fin < debut:  # Travail de nuit
                fin += timedelta(days=1)
            duree = fin - debut
            return duree.total_seconds() / 3600
        return 0

class Planning(db.Model):
    """Planning de travail"""
    __tablename__ = 'planning'
    
    id = db.Column(db.Integer, primary_key=True)
    personnel_id = db.Column(db.Integer, db.ForeignKey('personnel.id'), nullable=False)
    date_debut = db.Column(db.DateTime, nullable=False)
    date_fin = db.Column(db.DateTime, nullable=False)
    poste_travail = db.Column(db.String(100))
    unite_assignee = db.Column(db.String(100))
    type_service = db.Column(db.String(50))  # Jour, Nuit, Weekend
    statut = db.Column(db.String(50), default='Planifié')
    commentaires = db.Column(db.Text)

class Conge(db.Model):
    """Gestion des congés"""
    __tablename__ = 'conges'
    
    id = db.Column(db.Integer, primary_key=True)
    personnel_id = db.Column(db.Integer, db.ForeignKey('personnel.id'), nullable=False)
    type_conge = db.Column(db.String(50), nullable=False)  # Annuel, Maladie, Maternité, etc.
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=False)
    nombre_jours = db.Column(db.Integer)
    motif = db.Column(db.Text)
    statut = db.Column(db.String(50), default='En attente')  # En attente, Approuvé, Refusé
    date_demande = db.Column(db.DateTime, default=datetime.utcnow)
    approuve_par = db.Column(db.Integer, db.ForeignKey('personnel.id'))

class Competence(db.Model):
    """Référentiel des compétences"""
    __tablename__ = 'competences'
    
    id = db.Column(db.Integer, primary_key=True)
    nom = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    categorie = db.Column(db.String(100))  # Technique, Sécurité, Management
    niveau_requis = db.Column(db.String(50))  # Débutant, Intermédiaire, Expert

class CompetencePersonnel(db.Model):
    """Association personnel-compétences"""
    __tablename__ = 'competences_personnel'
    
    id = db.Column(db.Integer, primary_key=True)
    personnel_id = db.Column(db.Integer, db.ForeignKey('personnel.id'), nullable=False)
    competence_id = db.Column(db.Integer, db.ForeignKey('competences.id'), nullable=False)
    niveau_actuel = db.Column(db.String(50))  # Débutant, Intermédiaire, Expert
    date_acquisition = db.Column(db.Date)
    date_evaluation = db.Column(db.Date)
    certificat = db.Column(db.String(200))  # Chemin vers le certificat
    validite = db.Column(db.Date)  # Date d'expiration si applicable
