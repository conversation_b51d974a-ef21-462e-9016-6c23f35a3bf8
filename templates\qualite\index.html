{% extends "base.html" %}

{% block title %}Qualité - Gestion Unité Phosphate{% endblock %}
{% block page_title %}Contrôle Qualité{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4>Vue d'ensemble de la qualité</h4>
            <div>
                <a href="{{ url_for('qualite.nouveau_controle') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Nouveau contrôle
                </a>
                <a href="{{ url_for('qualite.parametres') }}" class="btn btn-outline-info">
                    <i class="fas fa-cog"></i> Paramètres
                </a>
                <a href="{{ url_for('qualite.indicateurs') }}" class="btn btn-outline-success">
                    <i class="fas fa-chart-line"></i> Indicateurs
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Cartes de statistiques -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Contrôles du jour
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ controles_today|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clipboard-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Taux de conformité
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ "%.1f"|format(taux_conformite) }}%
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Non-conformités
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ controles_non_conformes|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Actions ouvertes
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ actions_ouvertes|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tasks fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Contrôles du jour -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Contrôles du jour</h6>
                <a href="{{ url_for('qualite.controles') }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-eye"></i> Voir tous
                </a>
            </div>
            <div class="card-body">
                {% if controles_today %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>N° Contrôle</th>
                                    <th>Type</th>
                                    <th>Unité</th>
                                    <th>Statut</th>
                                    <th>Heure</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for controle in controles_today %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('qualite.detail_controle', controle_id=controle.id) }}">
                                            {{ controle.numero_controle }}
                                        </a>
                                    </td>
                                    <td>{{ controle.type_controle }}</td>
                                    <td>{{ controle.unite_production.nom if controle.unite_production else 'N/A' }}</td>
                                    <td>
                                        {% if controle.statut == 'Conforme' %}
                                            <span class="badge bg-success">{{ controle.statut }}</span>
                                        {% elif controle.statut == 'Non conforme' %}
                                            <span class="badge bg-danger">{{ controle.statut }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ controle.statut }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ controle.date_prelevement.strftime('%H:%M') if controle.date_prelevement else 'N/A' }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">Aucun contrôle effectué aujourd'hui.</p>
                    <a href="{{ url_for('qualite.nouveau_controle') }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus"></i> Effectuer un contrôle
                    </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Non-conformités en attente -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-warning">Non-conformités en attente</h6>
                <a href="{{ url_for('qualite.actions_correctives') }}" class="btn btn-sm btn-warning">
                    <i class="fas fa-eye"></i> Voir toutes
                </a>
            </div>
            <div class="card-body">
                {% if controles_non_conformes %}
                    <div class="list-group">
                        {% for controle in controles_non_conformes %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ controle.numero_controle }}</h6>
                                <small class="text-muted">
                                    {{ controle.type_controle }} - 
                                    {{ controle.date_prelevement.strftime('%d/%m/%Y %H:%M') if controle.date_prelevement else 'N/A' }}
                                </small>
                            </div>
                            <div>
                                <a href="{{ url_for('qualite.nouvelle_action_corrective', controle_id=controle.id) }}" 
                                   class="btn btn-sm btn-outline-warning">
                                    <i class="fas fa-plus"></i> Action
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> Aucune non-conformité en attente
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Actions correctives ouvertes -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-danger">Actions correctives ouvertes</h6>
                <a href="{{ url_for('qualite.nouvelle_action_corrective') }}" class="btn btn-sm btn-danger">
                    <i class="fas fa-plus"></i> Nouvelle action
                </a>
            </div>
            <div class="card-body">
                {% if actions_ouvertes %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Titre</th>
                                    <th>Priorité</th>
                                    <th>Responsable</th>
                                    <th>Échéance</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for action in actions_ouvertes %}
                                <tr>
                                    <td>
                                        <strong>{{ action.titre }}</strong><br>
                                        <small class="text-muted">{{ action.description[:50] }}...</small>
                                    </td>
                                    <td>
                                        {% if action.priorite == 'Urgente' %}
                                            <span class="badge bg-danger">{{ action.priorite }}</span>
                                        {% elif action.priorite == 'Haute' %}
                                            <span class="badge bg-warning">{{ action.priorite }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ action.priorite }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ action.responsable.nom if action.responsable else 'Non assigné' }}</td>
                                    <td>
                                        {% if action.date_echeance %}
                                            <small>{{ action.date_echeance.strftime('%d/%m/%Y') }}</small>
                                            {% if action.date_echeance < date.today() %}
                                                <span class="badge bg-danger">En retard</span>
                                            {% endif %}
                                        {% else %}
                                            <small class="text-muted">Non définie</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">{{ action.statut }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> Aucune action corrective ouverte
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Indicateurs qualité -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">Indicateurs qualité</h6>
            </div>
            <div class="card-body">
                <!-- Taux de conformité -->
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Taux de conformité</span>
                        <span class="font-weight-bold">{{ "%.1f"|format(taux_conformite) }}%</span>
                    </div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ taux_conformite }}%">
                        </div>
                    </div>
                </div>

                <!-- Objectif qualité -->
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Objectif qualité</span>
                        <span class="font-weight-bold">95%</span>
                    </div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-info" role="progressbar" style="width: 95%">
                        </div>
                    </div>
                </div>

                <hr>

                <!-- Actions rapides -->
                <div class="d-grid gap-2">
                    <a href="{{ url_for('qualite.controles') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-list"></i> Tous les contrôles
                    </a>
                    <a href="{{ url_for('qualite.parametres') }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-cog"></i> Gérer les paramètres
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Évolution de la qualité -->
    <div class="col-lg-12 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Évolution de la qualité (12 derniers mois)</h6>
            </div>
            <div class="card-body">
                <canvas id="qualityChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Graphique d'évolution de la qualité
const ctx = document.getElementById('qualityChart').getContext('2d');
const qualityChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'],
        datasets: [
            {
                label: 'Taux de conformité (%)',
                data: [92, 94, 91, 96, 93, 97, 94, 95, 92, 94, 96, 95],
                borderColor: 'rgb(28, 200, 138)',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                tension: 0.1,
                fill: true
            },
            {
                label: 'Objectif (95%)',
                data: [95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95, 95],
                borderColor: 'rgb(255, 99, 132)',
                borderDash: [5, 5],
                fill: false
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: false,
                min: 85,
                max: 100,
                title: {
                    display: true,
                    text: 'Pourcentage (%)'
                }
            }
        },
        plugins: {
            legend: {
                position: 'top',
            }
        }
    }
});
</script>
{% endblock %}
