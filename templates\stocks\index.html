{% extends "base.html" %}

{% block title %}Stocks - Gestion Unité Phosphate{% endblock %}
{% block page_title %}Gestion des Stocks{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4>Vue d'ensemble des stocks</h4>
            <div>
                <a href="{{ url_for('stocks.mouvement_stock') }}" class="btn btn-primary">
                    <i class="fas fa-exchange-alt"></i> Mouvement de stock
                </a>
                <a href="{{ url_for('stocks.nouvelle_livraison') }}" class="btn btn-outline-success">
                    <i class="fas fa-truck"></i> Nouvelle livraison
                </a>
                <a href="{{ url_for('stocks.nouvelle_matiere') }}" class="btn btn-outline-info">
                    <i class="fas fa-plus"></i> Nouvelle matière
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Cartes de statistiques -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Valeur totale stocks
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ "{:,.0f}".format(valeur_totale) }} DH
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-coins fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Stocks à réapprovisionner
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stocks_reappro|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Stocks critiques
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stocks_critiques|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Livraisons en attente
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ livraisons_attente|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-truck fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- État des stocks -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">État des stocks</h6>
                <a href="{{ url_for('stocks.matieres_premieres') }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-eye"></i> Voir toutes les matières
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Matière première</th>
                                <th>Stock actuel</th>
                                <th>Stock minimum</th>
                                <th>Valeur</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stock in stocks %}
                            <tr>
                                <td>
                                    <strong>{{ stock.matiere_premiere.nom if stock.matiere_premiere else 'N/A' }}</strong><br>
                                    <small class="text-muted">{{ stock.zone_stockage }} - {{ stock.emplacement }}</small>
                                </td>
                                <td>
                                    <span class="h6">{{ "%.1f"|format(stock.quantite_actuelle) }}</span>
                                    <small class="text-muted">{{ stock.matiere_premiere.unite_mesure if stock.matiere_premiere else '' }}</small>
                                </td>
                                <td>{{ "%.1f"|format(stock.quantite_minimum) }}</td>
                                <td>{{ "{:,.0f}".format(stock.valeur_stock or 0) }} DH</td>
                                <td>
                                    {% if stock.niveau_critique %}
                                        <span class="badge bg-danger">Critique</span>
                                    {% elif stock.besoin_reapprovisionnement %}
                                        <span class="badge bg-warning">À réapprovisionner</span>
                                    {% else %}
                                        <span class="badge bg-success">Normal</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('stocks.detail_stock', stock_id=stock.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Alertes et actions -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">Alertes stocks</h6>
            </div>
            <div class="card-body">
                {% if stocks_critiques %}
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> Stocks critiques</h6>
                        <ul class="mb-0">
                            {% for stock in stocks_critiques %}
                            <li>{{ stock.matiere_premiere.nom if stock.matiere_premiere else 'N/A' }} 
                                ({{ "%.1f"|format(stock.quantite_actuelle) }} {{ stock.matiere_premiere.unite_mesure if stock.matiere_premiere else '' }})</li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                {% if stocks_reappro %}
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-shopping-cart"></i> À réapprovisionner</h6>
                        <ul class="mb-0">
                            {% for stock in stocks_reappro %}
                            <li>{{ stock.matiere_premiere.nom if stock.matiere_premiere else 'N/A' }} 
                                ({{ "%.1f"|format(stock.quantite_actuelle) }}/{{ "%.1f"|format(stock.quantite_minimum) }})</li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                {% if livraisons_attente %}
                    <div class="alert alert-info">
                        <h6><i class="fas fa-truck"></i> Livraisons en transit</h6>
                        <ul class="mb-0">
                            {% for livraison in livraisons_attente %}
                            <li>{{ livraison.numero_livraison }} - {{ livraison.fournisseur.nom if livraison.fournisseur else 'N/A' }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                {% if not stocks_critiques and not stocks_reappro and not livraisons_attente %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> Tous les stocks sont normaux
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="card shadow mt-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Actions rapides</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('stocks.fournisseurs') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-building"></i> Gérer les fournisseurs
                    </a>
                    <a href="{{ url_for('stocks.livraisons') }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-truck"></i> Suivi des livraisons
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Évolution des stocks -->
    <div class="col-lg-12 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Évolution des stocks (30 derniers jours)</h6>
            </div>
            <div class="card-body">
                <canvas id="stockChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Graphique d'évolution des stocks
const ctx = document.getElementById('stockChart').getContext('2d');
const stockChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['J-30', 'J-25', 'J-20', 'J-15', 'J-10', 'J-5', 'Aujourd\'hui'],
        datasets: [
            {
                label: 'Phosphate brut (tonnes)',
                data: [520, 480, 450, 500, 520, 490, 500],
                borderColor: 'rgb(78, 115, 223)',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                tension: 0.1
            },
            {
                label: 'Eau industrielle (m³)',
                data: [45, 40, 35, 50, 55, 48, 50],
                borderColor: 'rgb(28, 200, 138)',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                tension: 0.1
            },
            {
                label: 'Floculant (kg)',
                data: [180, 170, 160, 200, 220, 190, 200],
                borderColor: 'rgb(54, 185, 204)',
                backgroundColor: 'rgba(54, 185, 204, 0.1)',
                tension: 0.1
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Quantité'
                }
            }
        },
        plugins: {
            legend: {
                position: 'top',
            }
        }
    }
});
</script>
{% endblock %}
