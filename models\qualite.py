from datetime import datetime
from database import db

class ParametreQualite(db.Model):
    """Paramètres de qualité à contrôler"""
    __tablename__ = 'parametres_qualite'
    
    id = db.Column(db.Integer, primary_key=True)
    nom = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    unite_mesure = db.Column(db.String(20))
    
    # Limites
    valeur_min = db.Column(db.Float)
    valeur_max = db.Column(db.Float)
    valeur_cible = db.Column(db.Float)
    tolerance = db.Column(db.Float)
    
    # Méthode d'analyse
    methode_analyse = db.Column(db.String(200))
    frequence_controle = db.Column(db.String(100))
    
    actif = db.Column(db.Boolean, default=True)

class ControleQualite(db.Model):
    """Contrôles qualité effectués"""
    __tablename__ = 'controles_qualite'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Référence
    numero_controle = db.Column(db.String(50), unique=True, nullable=False)
    type_controle = db.Column(db.String(50))  # Matière première, Produit fini, En cours
    
    # Échantillon
    unite_production_id = db.Column(db.Integer, db.ForeignKey('unites_production.id'))
    lot_production = db.Column(db.String(100))
    date_prelevement = db.Column(db.DateTime, nullable=False)
    lieu_prelevement = db.Column(db.String(100))
    
    # Analyse
    date_analyse = db.Column(db.DateTime)
    laboratoire = db.Column(db.String(100))
    technicien_id = db.Column(db.Integer, db.ForeignKey('personnel.id'))
    
    # Résultats globaux
    statut = db.Column(db.String(50), default='En cours')  # En cours, Conforme, Non conforme
    conforme = db.Column(db.Boolean)
    commentaires = db.Column(db.Text)
    
    date_creation = db.Column(db.DateTime, default=datetime.utcnow)

class ResultatControle(db.Model):
    """Résultats détaillés des contrôles"""
    __tablename__ = 'resultats_controle'
    
    id = db.Column(db.Integer, primary_key=True)
    controle_id = db.Column(db.Integer, db.ForeignKey('controles_qualite.id'), nullable=False)
    parametre_id = db.Column(db.Integer, db.ForeignKey('parametres_qualite.id'), nullable=False)
    
    # Résultat
    valeur_mesuree = db.Column(db.Float, nullable=False)
    conforme = db.Column(db.Boolean)
    ecart = db.Column(db.Float)  # Écart par rapport à la valeur cible
    
    # Méthode
    methode_utilisee = db.Column(db.String(200))
    incertitude = db.Column(db.Float)
    
    observations = db.Column(db.Text)

class ActionCorrective(db.Model):
    """Actions correctives suite aux non-conformités"""
    __tablename__ = 'actions_correctives'
    
    id = db.Column(db.Integer, primary_key=True)
    controle_id = db.Column(db.Integer, db.ForeignKey('controles_qualite.id'))
    
    # Description
    titre = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=False)
    cause_racine = db.Column(db.Text)
    
    # Responsabilités
    responsable_id = db.Column(db.Integer, db.ForeignKey('personnel.id'))
    priorite = db.Column(db.String(20), default='Normale')  # Urgente, Haute, Normale, Basse
    
    # Dates
    date_detection = db.Column(db.DateTime, default=datetime.utcnow)
    date_echeance = db.Column(db.DateTime)
    date_cloture = db.Column(db.DateTime)
    
    # Statut
    statut = db.Column(db.String(50), default='Ouverte')  # Ouverte, En cours, Fermée, Annulée
    
    # Actions
    actions_immediates = db.Column(db.Text)
    actions_preventives = db.Column(db.Text)
    verification_efficacite = db.Column(db.Text)
    
    cout_estime = db.Column(db.Float)
    cout_reel = db.Column(db.Float)

class CertificatAnalyse(db.Model):
    """Certificats d'analyse"""
    __tablename__ = 'certificats_analyse'
    
    id = db.Column(db.Integer, primary_key=True)
    controle_id = db.Column(db.Integer, db.ForeignKey('controles_qualite.id'), nullable=False)
    
    # Certificat
    numero_certificat = db.Column(db.String(100), unique=True, nullable=False)
    date_emission = db.Column(db.Date, nullable=False)
    valide_par = db.Column(db.Integer, db.ForeignKey('personnel.id'))
    
    # Fichier
    chemin_fichier = db.Column(db.String(500))  # Chemin vers le PDF du certificat
    
    # Validité
    date_validite = db.Column(db.Date)
    statut = db.Column(db.String(50), default='Valide')  # Valide, Expiré, Annulé
    
    commentaires = db.Column(db.Text)

class IndicateurQualite(db.Model):
    """Indicateurs qualité calculés"""
    __tablename__ = 'indicateurs_qualite'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Période
    periode = db.Column(db.String(20))  # Jour, Semaine, Mois
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=False)
    
    # Unité de production
    unite_production_id = db.Column(db.Integer, db.ForeignKey('unites_production.id'))
    
    # Indicateurs
    nombre_controles = db.Column(db.Integer, default=0)
    nombre_conformes = db.Column(db.Integer, default=0)
    nombre_non_conformes = db.Column(db.Integer, default=0)
    taux_conformite = db.Column(db.Float)  # %
    
    # Actions correctives
    nombre_actions_ouvertes = db.Column(db.Integer, default=0)
    nombre_actions_fermees = db.Column(db.Integer, default=0)
    delai_moyen_cloture = db.Column(db.Float)  # Jours
    
    date_calcul = db.Column(db.DateTime, default=datetime.utcnow)
