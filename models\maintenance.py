from datetime import datetime, timedelta
from database import db

class Maintenance(db.Model):
    """Interventions de maintenance"""
    __tablename__ = 'maintenances'
    
    id = db.Column(db.Integer, primary_key=True)
    equipement_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('equipements.id'), nullable=False)
    type_maintenance = db.Column(db.String(50), nullable=False)  # Corrective, Préventive
    priorite = db.Column(db.String(20), default='Normale')  # Urgente, Haute, Normale, Basse
    statut = db.Column(db.String(50), default='Planifiée')  # Planifiée, En cours, Terminée, Annulée
    
    # Dates
    date_demande = db.Column(db.DateTime, default=datetime.utcnow)
    date_planifiee = db.Column(db.DateTime)
    date_debut = db.Column(db.DateTime)
    date_fin = db.Column(db.DateTime)
    
    # Description
    description = db.Column(db.Text, nullable=False)
    diagnostic = db.Column(db.Text)
    travaux_realises = db.Column(db.Text)
    pieces_utilisees = db.Column(db.Text)
    
    # Personnel
    demandeur_id = db.Column(db.Integer, db.ForeignKey('personnel.id'))
    technicien_id = db.Column(db.Integer, db.ForeignKey('personnel.id'))
    
    # Coûts
    cout_main_oeuvre = db.Column(db.Float, default=0)
    cout_pieces = db.Column(db.Float, default=0)
    cout_total = db.Column(db.Float, default=0)
    
    # Temps
    duree_prevue = db.Column(db.Integer)  # Minutes
    duree_reelle = db.Column(db.Integer)  # Minutes
    
    commentaires = db.Column(db.Text)
    
    @property
    def duree_reelle_calculee(self):
        """Calcul automatique de la durée réelle"""
        if self.date_debut and self.date_fin:
            delta = self.date_fin - self.date_debut
            return int(delta.total_seconds() / 60)  # En minutes
        return None

class PlanMaintenance(db.Model):
    """Planning de maintenance préventive"""
    __tablename__ = 'plan_maintenance'
    
    id = db.Column(db.Integer, primary_key=True)
    equipement_id = db.Column(db.Integer, db.ForeignKey('equipements.id'), nullable=False)
    nom_plan = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    
    # Fréquence
    frequence_type = db.Column(db.String(50))  # Heures, Jours, Semaines, Mois
    frequence_valeur = db.Column(db.Integer)  # Nombre d'unités
    
    # Dates
    derniere_execution = db.Column(db.Date)
    prochaine_execution = db.Column(db.Date)
    
    # Détails
    duree_estimee = db.Column(db.Integer)  # Minutes
    pieces_necessaires = db.Column(db.Text)
    instructions = db.Column(db.Text)
    
    actif = db.Column(db.Boolean, default=True)
    date_creation = db.Column(db.DateTime, default=datetime.utcnow)
    
    def calculer_prochaine_execution(self):
        """Calcule la prochaine date d'exécution"""
        if not self.derniere_execution or not self.frequence_type or not self.frequence_valeur:
            return None
            
        if self.frequence_type == 'Jours':
            return self.derniere_execution + timedelta(days=self.frequence_valeur)
        elif self.frequence_type == 'Semaines':
            return self.derniere_execution + timedelta(weeks=self.frequence_valeur)
        elif self.frequence_type == 'Mois':
            return self.derniere_execution + timedelta(days=self.frequence_valeur * 30)
        
        return None

class PieceRechange(db.Model):
    """Pièces de rechange"""
    __tablename__ = 'pieces_rechange'
    
    id = db.Column(db.Integer, primary_key=True)
    nom = db.Column(db.String(200), nullable=False)
    reference = db.Column(db.String(100), unique=True)
    description = db.Column(db.Text)
    
    # Stock
    stock_actuel = db.Column(db.Integer, default=0)
    stock_minimum = db.Column(db.Integer, default=0)
    stock_maximum = db.Column(db.Integer, default=0)
    
    # Prix
    prix_unitaire = db.Column(db.Float)
    fournisseur = db.Column(db.String(200))
    
    # Localisation
    emplacement = db.Column(db.String(100))
    
    date_creation = db.Column(db.DateTime, default=datetime.utcnow)
    
    @property
    def besoin_reapprovisionnement(self):
        """Vérifie si un réapprovisionnement est nécessaire"""
        return self.stock_actuel <= self.stock_minimum
