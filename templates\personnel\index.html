{% extends "base.html" %}

{% block title %}Personnel - Gestion Unité Phosphate{% endblock %}
{% block page_title %}Gestion du Personnel{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4>Vue d'ensemble du personnel</h4>
            <div>
                <a href="{{ url_for('personnel.nouvel_employe') }}" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i> Nouvel employé
                </a>
                <a href="{{ url_for('personnel.presences') }}" class="btn btn-outline-success">
                    <i class="fas fa-clock"></i> Présences
                </a>
                <a href="{{ url_for('personnel.planning') }}" class="btn btn-outline-info">
                    <i class="fas fa-calendar"></i> Planning
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Cartes de statistiques -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Personnel actif
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ personnel_actif }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Présents aujourd'hui
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ personnel_present }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Congés en cours
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ conges_en_cours }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-times fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Planning semaine
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ planning_semaine|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-week fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Planning de la semaine -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Planning de la semaine</h6>
                <a href="{{ url_for('personnel.planning') }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-eye"></i> Voir le planning complet
                </a>
            </div>
            <div class="card-body">
                {% if planning_semaine %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Employé</th>
                                    <th>Poste</th>
                                    <th>Unité</th>
                                    <th>Service</th>
                                    <th>Période</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for planning in planning_semaine %}
                                <tr>
                                    <td>
                                        {% if planning.personnel %}
                                            {{ planning.personnel.prenom }} {{ planning.personnel.nom }}
                                        {% else %}
                                            Non assigné
                                        {% endif %}
                                    </td>
                                    <td>{{ planning.poste_travail or 'N/A' }}</td>
                                    <td>{{ planning.unite_assignee or 'N/A' }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'primary' if planning.type_service == 'Jour' else 'dark' if planning.type_service == 'Nuit' else 'warning' }}">
                                            {{ planning.type_service or 'N/A' }}
                                        </span>
                                    </td>
                                    <td>
                                        <small>
                                            {{ planning.date_debut.strftime('%d/%m %H:%M') if planning.date_debut else 'N/A' }} - 
                                            {{ planning.date_fin.strftime('%d/%m %H:%M') if planning.date_fin else 'N/A' }}
                                        </small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">Aucun planning défini pour cette semaine.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Présences du jour -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-success">Présences du jour</h6>
                <a href="{{ url_for('personnel.presences') }}" class="btn btn-sm btn-success">
                    <i class="fas fa-clock"></i> Gérer
                </a>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Taux de présence</span>
                        <span class="font-weight-bold">
                            {% if personnel_actif > 0 %}
                                {{ "%.1f"|format((personnel_present / personnel_actif) * 100) }}%
                            {% else %}
                                0%
                            {% endif %}
                        </span>
                    </div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {% if personnel_actif > 0 %}{{ (personnel_present / personnel_actif) * 100 }}{% else %}0{% endif %}%">
                        </div>
                    </div>
                </div>

                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <div class="h4 text-success">{{ personnel_present }}</div>
                            <small class="text-muted">Présents</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-danger">{{ personnel_actif - personnel_present }}</div>
                        <small class="text-muted">Absents</small>
                    </div>
                </div>

                <div class="mt-3">
                    <a href="{{ url_for('personnel.presences') }}" class="btn btn-outline-success btn-sm w-100">
                        <i class="fas fa-edit"></i> Saisir les présences
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Répartition par département -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Répartition par département</h6>
            </div>
            <div class="card-body">
                <canvas id="departmentChart" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Actions rapides</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('personnel.employes') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list"></i> Liste des employés
                    </a>
                    <a href="{{ url_for('personnel.demande_conge') }}" class="btn btn-outline-warning">
                        <i class="fas fa-calendar-plus"></i> Nouvelle demande de congé
                    </a>
                    <a href="{{ url_for('personnel.conges') }}" class="btn btn-outline-info">
                        <i class="fas fa-calendar-check"></i> Gérer les congés
                    </a>
                </div>

                <hr>

                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Rappels</h6>
                    <ul class="mb-0">
                        <li>Saisir les présences quotidiennes</li>
                        <li>Valider les demandes de congé en attente</li>
                        <li>Mettre à jour le planning hebdomadaire</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Graphique de répartition par département
const ctx = document.getElementById('departmentChart').getContext('2d');
const departmentChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['Production', 'Maintenance', 'Qualité', 'Administration'],
        datasets: [{
            data: [8, 4, 2, 2],
            backgroundColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e'
            ],
            hoverBackgroundColor: [
                '#2e59d9',
                '#17a673',
                '#2c9faf',
                '#f4b619'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
