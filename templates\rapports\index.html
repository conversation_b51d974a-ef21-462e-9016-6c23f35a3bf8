{% extends "base.html" %}

{% block title %}Rapports - Gestion Unité Phosphate{% endblock %}
{% block page_title %}Rapports et Analyses{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-cogs fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">Rapport Production</h5>
                <p class="card-text">Analyse détaillée de la production par unité et période</p>
                <a href="{{ url_for('rapports.rapport_production') }}" class="btn btn-primary">
                    <i class="fas fa-chart-line"></i> Voir le rapport
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-wrench fa-3x text-warning"></i>
                </div>
                <h5 class="card-title">Rapport Maintenance</h5>
                <p class="card-text">Suivi des interventions et coûts de maintenance</p>
                <a href="{{ url_for('rapports.rapport_maintenance') }}" class="btn btn-warning">
                    <i class="fas fa-tools"></i> Voir le rapport
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-users fa-3x text-success"></i>
                </div>
                <h5 class="card-title">Rapport Personnel</h5>
                <p class="card-text">Analyse des présences et performance du personnel</p>
                <a href="{{ url_for('rapports.rapport_personnel') }}" class="btn btn-success">
                    <i class="fas fa-user-check"></i> Voir le rapport
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-boxes fa-3x text-info"></i>
                </div>
                <h5 class="card-title">Rapport Stocks</h5>
                <p class="card-text">État des stocks et mouvements de matières premières</p>
                <a href="{{ url_for('rapports.rapport_stocks') }}" class="btn btn-info">
                    <i class="fas fa-warehouse"></i> Voir le rapport
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-check-circle fa-3x text-secondary"></i>
                </div>
                <h5 class="card-title">Rapport Qualité</h5>
                <p class="card-text">Contrôles qualité et taux de conformité</p>
                <a href="{{ url_for('rapports.rapport_qualite') }}" class="btn btn-secondary">
                    <i class="fas fa-clipboard-check"></i> Voir le rapport
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-lg-9">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tachometer-alt"></i>
                    Indicateurs Clés de Performance (KPI)
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="border-end">
                            <h3 class="text-primary">85.2%</h3>
                            <small class="text-muted">Taux de disponibilité</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border-end">
                            <h3 class="text-success">92.1%</h3>
                            <small class="text-muted">Taux de qualité</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border-end">
                            <h3 class="text-warning">78.5%</h3>
                            <small class="text-muted">Rendement global</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <h3 class="text-info">1,245 T</h3>
                        <small class="text-muted">Production mensuelle</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i>
                    Évolution des KPI (12 derniers mois)
                </h5>
            </div>
            <div class="card-body">
                <canvas id="kpiChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-download"></i>
                    Exports disponibles
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="exportData('production')">
                        <i class="fas fa-file-excel"></i> Export Production Excel
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="exportData('maintenance')">
                        <i class="fas fa-file-excel"></i> Export Maintenance Excel
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="exportData('personnel')">
                        <i class="fas fa-file-excel"></i> Export Personnel Excel
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="exportData('stocks')">
                        <i class="fas fa-file-excel"></i> Export Stocks Excel
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Graphique des KPI
const ctx = document.getElementById('kpiChart').getContext('2d');
const kpiChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'],
        datasets: [
            {
                label: 'Disponibilité (%)',
                data: [82, 85, 88, 84, 87, 89, 85, 86, 88, 85, 87, 85],
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.1
            },
            {
                label: 'Qualité (%)',
                data: [90, 92, 89, 93, 91, 94, 92, 93, 91, 92, 94, 92],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1
            },
            {
                label: 'Rendement (%)',
                data: [75, 78, 76, 80, 77, 82, 78, 79, 81, 78, 80, 79],
                borderColor: 'rgb(255, 206, 86)',
                backgroundColor: 'rgba(255, 206, 86, 0.1)',
                tension: 0.1
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                title: {
                    display: true,
                    text: 'Pourcentage (%)'
                }
            }
        },
        plugins: {
            legend: {
                position: 'top',
            }
        }
    }
});

// Fonction d'export
function exportData(type) {
    window.location.href = `/rapports/export_excel/${type}`;
}
</script>
{% endblock %}
