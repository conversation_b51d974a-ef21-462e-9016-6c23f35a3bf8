{% extends "base.html" %}

{% block title %}Mouvement de Stock - Gestion Unité Phosphate{% endblock %}
{% block page_title %}Mouvement de Stock{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4>Enregistrer un mouvement de stock</h4>
            <a href="{{ url_for('stocks.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Retour aux stocks
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Formulaire de mouvement -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-exchange-alt"></i> Nouveau mouvement
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" id="mouvementForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="stock_id" class="form-label">Matière première *</label>
                                <select class="form-select" id="stock_id" name="stock_id" required onchange="afficherInfoStock()">
                                    <option value="">Sélectionner une matière première</option>
                                    {% for stock in stocks %}
                                    <option value="{{ stock.id }}" 
                                            data-quantite="{{ stock.quantite_actuelle }}"
                                            data-unite="{{ stock.matiere_premiere.unite_mesure if stock.matiere_premiere else '' }}"
                                            data-minimum="{{ stock.quantite_minimum }}"
                                            data-zone="{{ stock.zone_stockage }}"
                                            data-emplacement="{{ stock.emplacement }}">
                                        {{ stock.matiere_premiere.nom if stock.matiere_premiere else 'N/A' }} 
                                        ({{ "%.1f"|format(stock.quantite_actuelle) }} {{ stock.matiere_premiere.unite_mesure if stock.matiere_premiere else '' }})
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type_mouvement" class="form-label">Type de mouvement *</label>
                                <select class="form-select" id="type_mouvement" name="type_mouvement" required onchange="changerTypeMouvement()">
                                    <option value="">Sélectionner le type</option>
                                    <option value="Entrée">Entrée (Réception)</option>
                                    <option value="Sortie">Sortie (Consommation)</option>
                                    <option value="Ajustement">Ajustement (Inventaire)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quantite" class="form-label">Quantité *</label>
                                <div class="input-group">
                                    <input type="number" step="0.1" class="form-control" id="quantite" name="quantite" 
                                           required min="0" onchange="calculerNouveauStock()">
                                    <span class="input-group-text" id="unite-mesure">-</span>
                                </div>
                                <div class="form-text" id="quantite-help"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="prix_unitaire" class="form-label">Prix unitaire (DH)</label>
                                <input type="number" step="0.01" class="form-control" id="prix_unitaire" name="prix_unitaire" 
                                       min="0" onchange="calculerValeur()">
                                <div class="form-text">Optionnel - pour les entrées</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date_mouvement" class="form-label">Date du mouvement *</label>
                                <input type="datetime-local" class="form-control" id="date_mouvement" name="date_mouvement" 
                                       value="{{ moment().format('YYYY-MM-DDTHH:mm') }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="responsable_id" class="form-label">Responsable</label>
                                <select class="form-select" id="responsable_id" name="responsable_id">
                                    <option value="">Sélectionner un responsable</option>
                                    {% for responsable in responsables %}
                                    <option value="{{ responsable.id }}">{{ responsable.prenom }} {{ responsable.nom }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="motif" class="form-label">Motif du mouvement *</label>
                        <input type="text" class="form-control" id="motif" name="motif" required 
                               placeholder="Ex: Livraison fournisseur, Consommation production, Ajustement inventaire">
                    </div>

                    <div class="mb-3">
                        <label for="commentaires" class="form-label">Commentaires</label>
                        <textarea class="form-control" id="commentaires" name="commentaires" rows="3" 
                                  placeholder="Informations complémentaires..."></textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('stocks.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Annuler
                        </a>
                        <button type="submit" class="btn btn-primary" id="btnSubmit">
                            <i class="fas fa-save"></i> Enregistrer le mouvement
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Informations sur le stock sélectionné -->
    <div class="col-lg-4">
        <div class="card" id="infoStock" style="display: none;">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-info-circle"></i> Informations stock
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Stock actuel</label>
                    <div class="h5 text-primary" id="stock-actuel">-</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Stock minimum</label>
                    <div class="h6 text-warning" id="stock-minimum">-</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Emplacement</label>
                    <div id="stock-emplacement">-</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Nouveau stock (après mouvement)</label>
                    <div class="h5" id="nouveau-stock">-</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Valeur du mouvement</label>
                    <div class="h6 text-success" id="valeur-mouvement">-</div>
                </div>
                
                <div id="alerte-stock" class="alert" style="display: none;"></div>
            </div>
        </div>

        <!-- Mouvements récents -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-secondary">
                    <i class="fas fa-history"></i> Mouvements récents
                </h6>
            </div>
            <div class="card-body">
                {% if mouvements_recents %}
                    <div class="timeline">
                        {% for mouvement in mouvements_recents[:5] %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-{{ 'success' if mouvement.type_mouvement == 'Entrée' else 'danger' if mouvement.type_mouvement == 'Sortie' else 'info' }}"></div>
                            <div class="timeline-content">
                                <div class="d-flex justify-content-between">
                                    <strong>{{ mouvement.type_mouvement }}</strong>
                                    <small class="text-muted">{{ mouvement.date_mouvement.strftime('%d/%m %H:%M') }}</small>
                                </div>
                                <div>{{ mouvement.stock.matiere_premiere.nom if mouvement.stock and mouvement.stock.matiere_premiere else 'N/A' }}</div>
                                <div class="text-{{ 'success' if mouvement.type_mouvement == 'Entrée' else 'danger' }}">
                                    {{ '+' if mouvement.type_mouvement == 'Entrée' else '-' if mouvement.type_mouvement == 'Sortie' else '±' }}{{ "%.1f"|format(abs(mouvement.quantite)) }}
                                </div>
                                <small class="text-muted">{{ mouvement.motif }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center">Aucun mouvement récent</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}

.alert {
    border-radius: 8px;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let stockData = {};

// Afficher les informations du stock sélectionné
function afficherInfoStock() {
    const select = document.getElementById('stock_id');
    const option = select.options[select.selectedIndex];
    const infoDiv = document.getElementById('infoStock');
    
    if (option.value) {
        stockData = {
            quantite: parseFloat(option.dataset.quantite),
            unite: option.dataset.unite,
            minimum: parseFloat(option.dataset.minimum),
            zone: option.dataset.zone,
            emplacement: option.dataset.emplacement
        };
        
        document.getElementById('stock-actuel').textContent = 
            stockData.quantite.toFixed(1) + ' ' + stockData.unite;
        document.getElementById('stock-minimum').textContent = 
            stockData.minimum.toFixed(1) + ' ' + stockData.unite;
        document.getElementById('stock-emplacement').textContent = 
            stockData.zone + ' - ' + stockData.emplacement;
        document.getElementById('unite-mesure').textContent = stockData.unite;
        
        infoDiv.style.display = 'block';
        calculerNouveauStock();
    } else {
        infoDiv.style.display = 'none';
        document.getElementById('unite-mesure').textContent = '-';
    }
}

// Changer le type de mouvement
function changerTypeMouvement() {
    const type = document.getElementById('type_mouvement').value;
    const quantiteInput = document.getElementById('quantite');
    const quantiteHelp = document.getElementById('quantite-help');
    const prixInput = document.getElementById('prix_unitaire');
    
    if (type === 'Sortie') {
        quantiteHelp.textContent = 'Quantité à sortir du stock';
        quantiteInput.max = stockData.quantite || '';
        prixInput.disabled = true;
        prixInput.value = '';
    } else if (type === 'Entrée') {
        quantiteHelp.textContent = 'Quantité à ajouter au stock';
        quantiteInput.removeAttribute('max');
        prixInput.disabled = false;
    } else if (type === 'Ajustement') {
        quantiteHelp.textContent = 'Quantité d\'ajustement (+ ou -)';
        quantiteInput.removeAttribute('max');
        prixInput.disabled = true;
        prixInput.value = '';
    }
    
    calculerNouveauStock();
}

// Calculer le nouveau stock après mouvement
function calculerNouveauStock() {
    const type = document.getElementById('type_mouvement').value;
    const quantite = parseFloat(document.getElementById('quantite').value) || 0;
    
    if (!stockData.quantite || !type) return;
    
    let nouveauStock = stockData.quantite;
    
    if (type === 'Entrée') {
        nouveauStock += quantite;
    } else if (type === 'Sortie') {
        nouveauStock -= quantite;
    } else if (type === 'Ajustement') {
        // Pour l'ajustement, la quantité peut être positive ou négative
        nouveauStock += quantite;
    }
    
    const nouveauStockDiv = document.getElementById('nouveau-stock');
    nouveauStockDiv.textContent = nouveauStock.toFixed(1) + ' ' + stockData.unite;
    
    // Changer la couleur selon le niveau
    if (nouveauStock < 0) {
        nouveauStockDiv.className = 'h5 text-danger';
        afficherAlerte('Stock négatif ! Vérifiez la quantité saisie.', 'danger');
    } else if (nouveauStock < stockData.minimum) {
        nouveauStockDiv.className = 'h5 text-warning';
        afficherAlerte('Attention : Le stock sera en dessous du minimum après ce mouvement.', 'warning');
    } else {
        nouveauStockDiv.className = 'h5 text-success';
        masquerAlerte();
    }
    
    calculerValeur();
}

// Calculer la valeur du mouvement
function calculerValeur() {
    const quantite = parseFloat(document.getElementById('quantite').value) || 0;
    const prix = parseFloat(document.getElementById('prix_unitaire').value) || 0;
    const valeur = quantite * prix;
    
    const valeurDiv = document.getElementById('valeur-mouvement');
    if (valeur > 0) {
        valeurDiv.textContent = valeur.toFixed(2) + ' DH';
    } else {
        valeurDiv.textContent = '-';
    }
}

// Afficher une alerte
function afficherAlerte(message, type) {
    const alerteDiv = document.getElementById('alerte-stock');
    alerteDiv.className = `alert alert-${type}`;
    alerteDiv.textContent = message;
    alerteDiv.style.display = 'block';
}

// Masquer l'alerte
function masquerAlerte() {
    document.getElementById('alerte-stock').style.display = 'none';
}

// Validation du formulaire
document.getElementById('mouvementForm').addEventListener('submit', function(e) {
    const type = document.getElementById('type_mouvement').value;
    const quantite = parseFloat(document.getElementById('quantite').value) || 0;
    
    if (type === 'Sortie' && quantite > stockData.quantite) {
        e.preventDefault();
        alert('Erreur : La quantité de sortie ne peut pas être supérieure au stock disponible.');
        return false;
    }
    
    const nouveauStock = type === 'Entrée' ? stockData.quantite + quantite : 
                        type === 'Sortie' ? stockData.quantite - quantite : 
                        stockData.quantite + quantite;
    
    if (nouveauStock < 0) {
        e.preventDefault();
        alert('Erreur : Ce mouvement rendrait le stock négatif.');
        return false;
    }
});

// Initialiser la date actuelle
document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    const dateString = now.getFullYear() + '-' + 
                      String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                      String(now.getDate()).padStart(2, '0') + 'T' + 
                      String(now.getHours()).padStart(2, '0') + ':' + 
                      String(now.getMinutes()).padStart(2, '0');
    document.getElementById('date_mouvement').value = dateString;
});
</script>
{% endblock %}
