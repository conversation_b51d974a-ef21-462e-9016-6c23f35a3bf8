from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.qualite import ParametreQualite, ControleQualite, ResultatControle, ActionCorrective, CertificatAnalyse, IndicateurQualite
from models.production import UniteProduction
from models.personnel import Personnel
from database import db
from datetime import datetime, date, timedelta

qualite_bp = Blueprint('qualite', __name__)

@qualite_bp.route('/')
@login_required
def index():
    """Vue d'ensemble de la qualité"""
    # Contrôles du jour
    today = date.today()
    controles_today = ControleQualite.query.filter(
        ControleQualite.date_prelevement >= today,
        ControleQualite.date_prelevement < today + timedelta(days=1)
    ).all()
    
    # Contrôles non conformes en attente
    controles_non_conformes = ControleQualite.query.filter_by(
        conforme=False,
        statut='En cours'
    ).all()
    
    # Actions correctives ouvertes
    actions_ouvertes = ActionCorrective.query.filter_by(statut='Ouverte').all()
    
    # Taux de conformité du mois
    start_month = today.replace(day=1)
    controles_mois = ControleQualite.query.filter(
        ControleQualite.date_analyse >= start_month,
        ControleQualite.statut == 'Conforme'
    ).all()
    
    total_controles = len([c for c in controles_mois if c.conforme is not None])
    controles_conformes = len([c for c in controles_mois if c.conforme])
    taux_conformite = (controles_conformes / total_controles * 100) if total_controles > 0 else 0
    
    return render_template('qualite/index.html',
                         controles_today=controles_today,
                         controles_non_conformes=controles_non_conformes,
                         actions_ouvertes=actions_ouvertes,
                         taux_conformite=taux_conformite)

@qualite_bp.route('/parametres')
@login_required
def parametres():
    """Gestion des paramètres de qualité"""
    parametres = ParametreQualite.query.filter_by(actif=True).all()
    return render_template('qualite/parametres.html', parametres=parametres)

@qualite_bp.route('/nouveau_parametre', methods=['GET', 'POST'])
@login_required
def nouveau_parametre():
    """Ajout d'un nouveau paramètre de qualité"""
    if request.method == 'POST':
        try:
            parametre = ParametreQualite(
                nom=request.form['nom'],
                description=request.form['description'],
                unite_mesure=request.form['unite_mesure'],
                valeur_min=float(request.form['valeur_min']) if request.form['valeur_min'] else None,
                valeur_max=float(request.form['valeur_max']) if request.form['valeur_max'] else None,
                valeur_cible=float(request.form['valeur_cible']) if request.form['valeur_cible'] else None,
                tolerance=float(request.form['tolerance']) if request.form['tolerance'] else None,
                methode_analyse=request.form['methode_analyse'],
                frequence_controle=request.form['frequence_controle']
            )
            
            db.session.add(parametre)
            db.session.commit()
            flash('Paramètre de qualité ajouté avec succès', 'success')
            return redirect(url_for('qualite.parametres'))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de l\'ajout: {str(e)}', 'error')
    
    return render_template('qualite/nouveau_parametre.html')

@qualite_bp.route('/controles')
@login_required
def controles():
    """Liste des contrôles qualité"""
    page = request.args.get('page', 1, type=int)
    type_filter = request.args.get('type', '')
    statut_filter = request.args.get('statut', '')
    
    query = ControleQualite.query
    
    if type_filter:
        query = query.filter_by(type_controle=type_filter)
    if statut_filter:
        query = query.filter_by(statut=statut_filter)
    
    controles = query.order_by(ControleQualite.date_prelevement.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('qualite/controles.html',
                         controles=controles,
                         type_filter=type_filter,
                         statut_filter=statut_filter)

@qualite_bp.route('/nouveau_controle', methods=['GET', 'POST'])
@login_required
def nouveau_controle():
    """Création d'un nouveau contrôle qualité"""
    if request.method == 'POST':
        try:
            controle = ControleQualite(
                numero_controle=request.form['numero_controle'],
                type_controle=request.form['type_controle'],
                unite_production_id=request.form['unite_production_id'] if request.form['unite_production_id'] else None,
                lot_production=request.form['lot_production'],
                date_prelevement=datetime.strptime(request.form['date_prelevement'], '%Y-%m-%dT%H:%M'),
                lieu_prelevement=request.form['lieu_prelevement'],
                laboratoire=request.form['laboratoire'],
                technicien_id=request.form['technicien_id'] if request.form['technicien_id'] else None,
                commentaires=request.form['commentaires']
            )
            
            db.session.add(controle)
            db.session.commit()
            flash('Contrôle qualité créé avec succès', 'success')
            return redirect(url_for('qualite.detail_controle', controle_id=controle.id))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la création: {str(e)}', 'error')
    
    unites = UniteProduction.query.all()
    techniciens = Personnel.query.filter_by(statut='Actif').all()
    
    return render_template('qualite/nouveau_controle.html',
                         unites=unites,
                         techniciens=techniciens)

@qualite_bp.route('/controle/<int:controle_id>')
@login_required
def detail_controle(controle_id):
    """Détail d'un contrôle qualité"""
    controle = ControleQualite.query.get_or_404(controle_id)
    parametres = ParametreQualite.query.filter_by(actif=True).all()
    
    return render_template('qualite/detail_controle.html',
                         controle=controle,
                         parametres=parametres)

@qualite_bp.route('/saisie_resultats/<int:controle_id>', methods=['POST'])
@login_required
def saisie_resultats(controle_id):
    """Saisie des résultats d'un contrôle"""
    try:
        controle = ControleQualite.query.get_or_404(controle_id)
        
        # Supprimer les anciens résultats
        ResultatControle.query.filter_by(controle_id=controle_id).delete()
        
        conforme_global = True
        
        for parametre_id in request.form.getlist('parametre_ids'):
            valeur_mesuree = float(request.form[f'valeur_{parametre_id}'])
            parametre = ParametreQualite.query.get(parametre_id)
            
            # Vérifier la conformité
            conforme = True
            ecart = 0
            
            if parametre.valeur_cible:
                ecart = abs(valeur_mesuree - parametre.valeur_cible)
                if parametre.tolerance and ecart > parametre.tolerance:
                    conforme = False
            
            if parametre.valeur_min and valeur_mesuree < parametre.valeur_min:
                conforme = False
            if parametre.valeur_max and valeur_mesuree > parametre.valeur_max:
                conforme = False
            
            if not conforme:
                conforme_global = False
            
            # Créer le résultat
            resultat = ResultatControle(
                controle_id=controle_id,
                parametre_id=parametre_id,
                valeur_mesuree=valeur_mesuree,
                conforme=conforme,
                ecart=ecart,
                methode_utilisee=request.form.get(f'methode_{parametre_id}', ''),
                observations=request.form.get(f'observations_{parametre_id}', '')
            )
            
            db.session.add(resultat)
        
        # Mettre à jour le contrôle
        controle.date_analyse = datetime.utcnow()
        controle.conforme = conforme_global
        controle.statut = 'Conforme' if conforme_global else 'Non conforme'
        
        db.session.commit()
        flash('Résultats enregistrés avec succès', 'success')

        # Si non conforme, rediriger vers la création d'action corrective
        if not conforme_global:
            return redirect(url_for('qualite.nouvelle_action_corrective', controle_id=controle_id))

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'enregistrement: {str(e)}', 'error')
    
    return redirect(url_for('qualite.detail_controle', controle_id=controle_id))

@qualite_bp.route('/actions_correctives')
@login_required
def actions_correctives():
    """Liste des actions correctives"""
    page = request.args.get('page', 1, type=int)
    statut_filter = request.args.get('statut', '')
    
    query = ActionCorrective.query
    
    if statut_filter:
        query = query.filter_by(statut=statut_filter)
    
    actions = query.order_by(ActionCorrective.date_detection.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('qualite/actions_correctives.html',
                         actions=actions,
                         statut_filter=statut_filter)

@qualite_bp.route('/nouvelle_action_corrective', methods=['GET', 'POST'])
@login_required
def nouvelle_action_corrective():
    """Création d'une nouvelle action corrective"""
    controle_id = request.args.get('controle_id')
    
    if request.method == 'POST':
        try:
            action = ActionCorrective(
                controle_id=request.form['controle_id'] if request.form['controle_id'] else None,
                titre=request.form['titre'],
                description=request.form['description'],
                cause_racine=request.form['cause_racine'],
                responsable_id=request.form['responsable_id'] if request.form['responsable_id'] else None,
                priorite=request.form['priorite'],
                date_echeance=datetime.strptime(request.form['date_echeance'], '%Y-%m-%d') if request.form['date_echeance'] else None,
                actions_immediates=request.form['actions_immediates'],
                actions_preventives=request.form['actions_preventives'],
                cout_estime=float(request.form['cout_estime']) if request.form['cout_estime'] else None
            )
            
            db.session.add(action)
            db.session.commit()
            flash('Action corrective créée avec succès', 'success')
            return redirect(url_for('qualite.actions_correctives'))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la création: {str(e)}', 'error')
    
    controles = ControleQualite.query.filter_by(conforme=False).all()
    responsables = Personnel.query.filter_by(statut='Actif').all()
    
    return render_template('qualite/nouvelle_action_corrective.html',
                         controles=controles,
                         responsables=responsables,
                         controle_id=controle_id)

@qualite_bp.route('/indicateurs')
@login_required
def indicateurs():
    """Indicateurs qualité"""
    # Indicateurs du mois
    today = date.today()
    start_month = today.replace(day=1)
    
    indicateurs_mois = IndicateurQualite.query.filter(
        IndicateurQualite.date_debut >= start_month,
        IndicateurQualite.periode == 'Mois'
    ).all()
    
    return render_template('qualite/indicateurs.html',
                         indicateurs_mois=indicateurs_mois)

@qualite_bp.route('/api/qualite_trends')
@login_required
def api_qualite_trends():
    """API pour les tendances qualité"""
    # Données des 12 derniers mois
    today = date.today()
    start_date = today.replace(day=1) - timedelta(days=365)
    
    controles = ControleQualite.query.filter(
        ControleQualite.date_analyse >= start_date,
        ControleQualite.conforme.isnot(None)
    ).all()
    
    # Grouper par mois
    trends_mensuelles = {}
    for controle in controles:
        mois = controle.date_analyse.strftime('%Y-%m')
        if mois not in trends_mensuelles:
            trends_mensuelles[mois] = {'total': 0, 'conformes': 0}
        
        trends_mensuelles[mois]['total'] += 1
        if controle.conforme:
            trends_mensuelles[mois]['conformes'] += 1
    
    # Calculer les taux de conformité
    for mois in trends_mensuelles:
        total = trends_mensuelles[mois]['total']
        conformes = trends_mensuelles[mois]['conformes']
        trends_mensuelles[mois]['taux'] = (conformes / total * 100) if total > 0 else 0
    
    return jsonify(trends_mensuelles)
