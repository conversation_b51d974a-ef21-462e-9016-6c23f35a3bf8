{% extends "base.html" %}

{% block title %}Liste des Employés - Gestion Unité Phosphate{% endblock %}
{% block page_title %}Liste des Employés{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4>Gestion des employés</h4>
            <a href="{{ url_for('personnel.nouvel_employe') }}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Nouvel employé
            </a>
        </div>
    </div>
</div>

<!-- Filtres de recherche -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Rechercher</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="Nom, prénom ou matricule">
            </div>
            <div class="col-md-3">
                <label for="departement" class="form-label">Département</label>
                <select class="form-select" id="departement" name="departement">
                    <option value="">Tous les départements</option>
                    {% for dept in departements %}
                    <option value="{{ dept }}" {% if dept == departement %}selected{% endif %}>
                        {{ dept }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="statut" class="form-label">Statut</label>
                <select class="form-select" id="statut" name="statut">
                    <option value="">Tous les statuts</option>
                    <option value="Actif">Actif</option>
                    <option value="Congé">En congé</option>
                    <option value="Arrêt maladie">Arrêt maladie</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search"></i> Rechercher
                </button>
                <a href="{{ url_for('personnel.employes') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Liste des employés -->
<div class="card">
    <div class="card-header">
        <h6 class="m-0 font-weight-bold text-primary">
            Liste des employés 
            <span class="badge bg-secondary">{{ employes.total }} employé(s)</span>
        </h6>
    </div>
    <div class="card-body">
        {% if employes.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Photo</th>
                            <th>Matricule</th>
                            <th>Nom complet</th>
                            <th>Poste</th>
                            <th>Département</th>
                            <th>Téléphone</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employe in employes.items %}
                        <tr>
                            <td>
                                <div class="avatar-circle">
                                    {{ employe.prenom[0] }}{{ employe.nom[0] }}
                                </div>
                            </td>
                            <td>
                                <strong>{{ employe.matricule }}</strong>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ employe.prenom }} {{ employe.nom }}</strong>
                                    {% if employe.email %}
                                        <br><small class="text-muted">{{ employe.email }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>{{ employe.poste }}</td>
                            <td>
                                <span class="badge bg-info">{{ employe.departement }}</span>
                            </td>
                            <td>{{ employe.telephone or 'N/A' }}</td>
                            <td>
                                {% if employe.statut == 'Actif' %}
                                    <span class="badge bg-success">{{ employe.statut }}</span>
                                {% elif employe.statut == 'Congé' %}
                                    <span class="badge bg-warning">{{ employe.statut }}</span>
                                {% elif employe.statut == 'Arrêt maladie' %}
                                    <span class="badge bg-danger">{{ employe.statut }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ employe.statut }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('personnel.detail_employe', employe_id=employe.id) }}" 
                                       class="btn btn-sm btn-outline-primary" title="Voir le détail">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('personnel.modifier_employe', employe_id=employe.id) }}" 
                                       class="btn btn-sm btn-outline-warning" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if employes.pages > 1 %}
            <nav aria-label="Navigation des pages">
                <ul class="pagination justify-content-center">
                    {% if employes.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('personnel.employes', page=employes.prev_num, search=search, departement=departement) }}">
                                Précédent
                            </a>
                        </li>
                    {% endif %}
                    
                    {% for page_num in employes.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != employes.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('personnel.employes', page=page_num, search=search, departement=departement) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                            {% endif %}
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if employes.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('personnel.employes', page=employes.next_num, search=search, departement=departement) }}">
                                Suivant
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Aucun employé trouvé</h5>
                <p class="text-muted">
                    {% if search or departement %}
                        Aucun employé ne correspond à vos critères de recherche.
                    {% else %}
                        Commencez par ajouter des employés à votre équipe.
                    {% endif %}
                </p>
                <a href="{{ url_for('personnel.nouvel_employe') }}" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i> Ajouter un employé
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ employes.total }}</h5>
                <p class="card-text">Total employés</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">
                    {{ employes.items | selectattr('statut', 'equalto', 'Actif') | list | length }}
                </h5>
                <p class="card-text">Employés actifs</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">
                    {{ employes.items | selectattr('statut', 'equalto', 'Congé') | list | length }}
                </h5>
                <p class="card-text">En congé</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">
                    {{ departements | length }}
                </h5>
                <p class="card-text">Départements</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    margin-right: 2px;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
</style>
{% endblock %}
