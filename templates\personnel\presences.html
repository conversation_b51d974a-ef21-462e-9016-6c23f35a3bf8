{% extends "base.html" %}

{% block title %}Gestion des Présences - Gestion Unité Phosphate{% endblock %}
{% block page_title %}Gestion des Présences{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4>Pointage du personnel</h4>
            <div>
                <button type="button" class="btn btn-success" onclick="marquerTousPresents()">
                    <i class="fas fa-check-double"></i> Marquer tous présents
                </button>
                <button type="button" class="btn btn-primary" onclick="sauvegarderPresences()">
                    <i class="fas fa-save"></i> Sauvegarder
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Sélection de la date -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label for="date" class="form-label">Date de pointage</label>
                <input type="date" class="form-control" id="date" name="date" 
                       value="{{ date_filter }}" onchange="this.form.submit()">
            </div>
            <div class="col-md-3">
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changerDate(-1)">
                        <i class="fas fa-chevron-left"></i> Jour précédent
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changerDate(1)">
                        Jour suivant <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-6">
                <div class="alert alert-info mb-0">
                    <i class="fas fa-info-circle"></i>
                    <strong>{{ date_filter | strftime('%A %d %B %Y') }}</strong> - 
                    Pointage pour {{ personnel_actif | length }} employé(s) actif(s)
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Formulaire de présences -->
<form id="presenceForm" method="POST" action="{{ url_for('personnel.saisie_presence') }}">
    <input type="hidden" name="date" value="{{ date_filter }}">
    
    <div class="card">
        <div class="card-header">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="m-0 font-weight-bold text-primary">
                        Feuille de présence - {{ date_filter | strftime('%d/%m/%Y') }}
                    </h6>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-success" onclick="filtrerStatut('present')">
                            <i class="fas fa-check"></i> Présents
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="filtrerStatut('absent')">
                            <i class="fas fa-times"></i> Absents
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="filtrerStatut('retard')">
                            <i class="fas fa-clock"></i> Retards
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="filtrerStatut('tous')">
                            <i class="fas fa-list"></i> Tous
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="presenceTable">
                    <thead>
                        <tr>
                            <th>Employé</th>
                            <th>Département</th>
                            <th>Heure d'arrivée</th>
                            <th>Heure de départ</th>
                            <th>Statut</th>
                            <th>Heures travaillées</th>
                            <th>Commentaires</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for employe in personnel_actif %}
                        {% set presence = presences_dict.get(employe.id) %}
                        <tr data-employe-id="{{ employe.id }}" class="presence-row">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle me-2">
                                        {{ employe.prenom[0] }}{{ employe.nom[0] }}
                                    </div>
                                    <div>
                                        <strong>{{ employe.prenom }} {{ employe.nom }}</strong><br>
                                        <small class="text-muted">{{ employe.matricule }}</small>
                                    </div>
                                </div>
                                <input type="hidden" name="personnel_ids" value="{{ employe.id }}">
                            </td>
                            <td>
                                <span class="badge bg-info">{{ employe.departement }}</span>
                            </td>
                            <td>
                                <input type="time" class="form-control form-control-sm" 
                                       name="heure_arrivee_{{ employe.id }}" 
                                       value="{{ presence.heure_arrivee.strftime('%H:%M') if presence and presence.heure_arrivee else '' }}"
                                       onchange="calculerHeures({{ employe.id }})">
                            </td>
                            <td>
                                <input type="time" class="form-control form-control-sm" 
                                       name="heure_depart_{{ employe.id }}" 
                                       value="{{ presence.heure_depart.strftime('%H:%M') if presence and presence.heure_depart else '' }}"
                                       onchange="calculerHeures({{ employe.id }})">
                            </td>
                            <td>
                                <select class="form-select form-select-sm statut-select" 
                                        name="statut_{{ employe.id }}" 
                                        onchange="changerStatut({{ employe.id }}, this.value)">
                                    <option value="Présent" {% if not presence or presence.statut == 'Présent' %}selected{% endif %}>
                                        Présent
                                    </option>
                                    <option value="Absent" {% if presence and presence.statut == 'Absent' %}selected{% endif %}>
                                        Absent
                                    </option>
                                    <option value="Retard" {% if presence and presence.statut == 'Retard' %}selected{% endif %}>
                                        En retard
                                    </option>
                                    <option value="Congé" {% if presence and presence.statut == 'Congé' %}selected{% endif %}>
                                        En congé
                                    </option>
                                </select>
                            </td>
                            <td>
                                <span id="heures_{{ employe.id }}" class="badge bg-secondary">
                                    {% if presence and presence.heures_travaillees %}
                                        {{ "%.1f"|format(presence.heures_travaillees) }}h
                                    {% else %}
                                        0h
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                <input type="text" class="form-control form-control-sm" 
                                       name="commentaires_{{ employe.id }}" 
                                       value="{{ presence.commentaires if presence else '' }}"
                                       placeholder="Commentaires...">
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer">
            <div class="row">
                <div class="col-md-6">
                    <div class="d-flex gap-3">
                        <div>
                            <span class="badge bg-success">Présents: <span id="count-present">0</span></span>
                        </div>
                        <div>
                            <span class="badge bg-danger">Absents: <span id="count-absent">0</span></span>
                        </div>
                        <div>
                            <span class="badge bg-warning">Retards: <span id="count-retard">0</span></span>
                        </div>
                        <div>
                            <span class="badge bg-info">Congés: <span id="count-conge">0</span></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer les présences
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Résumé de la journée -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">Résumé de la journée</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border-end">
                            <h4 class="text-success" id="total-present">0</h4>
                            <small class="text-muted">Présents</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h4 class="text-danger" id="total-absent">0</h4>
                            <small class="text-muted">Absents</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h4 class="text-warning" id="total-retard">0</h4>
                            <small class="text-muted">En retard</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-primary" id="taux-presence">0%</h4>
                        <small class="text-muted">Taux de présence</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 12px;
}

.table td {
    vertical-align: middle;
}

.form-control-sm, .form-select-sm {
    font-size: 0.875rem;
}

.presence-row.filtered {
    display: none;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Calculer les heures travaillées
function calculerHeures(employeId) {
    const arrivee = document.querySelector(`input[name="heure_arrivee_${employeId}"]`).value;
    const depart = document.querySelector(`input[name="heure_depart_${employeId}"]`).value;
    
    if (arrivee && depart) {
        const [hA, mA] = arrivee.split(':').map(Number);
        const [hD, mD] = depart.split(':').map(Number);
        
        const minutesArrivee = hA * 60 + mA;
        const minutesDepart = hD * 60 + mD;
        
        if (minutesDepart > minutesArrivee) {
            const heuresTravaillees = (minutesDepart - minutesArrivee) / 60;
            document.getElementById(`heures_${employeId}`).textContent = heuresTravaillees.toFixed(1) + 'h';
        }
    }
}

// Changer le statut d'un employé
function changerStatut(employeId, statut) {
    const row = document.querySelector(`tr[data-employe-id="${employeId}"]`);
    
    // Désactiver les champs d'heure si absent ou en congé
    const arriveeInput = row.querySelector(`input[name="heure_arrivee_${employeId}"]`);
    const departInput = row.querySelector(`input[name="heure_depart_${employeId}"]`);
    
    if (statut === 'Absent' || statut === 'Congé') {
        arriveeInput.disabled = true;
        departInput.disabled = true;
        arriveeInput.value = '';
        departInput.value = '';
        document.getElementById(`heures_${employeId}`).textContent = '0h';
    } else {
        arriveeInput.disabled = false;
        departInput.disabled = false;
    }
    
    mettreAJourCompteurs();
}

// Marquer tous les employés comme présents
function marquerTousPresents() {
    document.querySelectorAll('.statut-select').forEach(select => {
        select.value = 'Présent';
        const employeId = select.name.split('_')[1];
        changerStatut(employeId, 'Présent');
    });
}

// Filtrer par statut
function filtrerStatut(statut) {
    const rows = document.querySelectorAll('.presence-row');
    
    rows.forEach(row => {
        const select = row.querySelector('.statut-select');
        const statutActuel = select.value;
        
        if (statut === 'tous') {
            row.style.display = '';
        } else if (statut === 'present' && statutActuel === 'Présent') {
            row.style.display = '';
        } else if (statut === 'absent' && statutActuel === 'Absent') {
            row.style.display = '';
        } else if (statut === 'retard' && statutActuel === 'Retard') {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Mettre à jour les compteurs
function mettreAJourCompteurs() {
    const statuts = {
        'Présent': 0,
        'Absent': 0,
        'Retard': 0,
        'Congé': 0
    };
    
    document.querySelectorAll('.statut-select').forEach(select => {
        statuts[select.value]++;
    });
    
    document.getElementById('count-present').textContent = statuts['Présent'];
    document.getElementById('count-absent').textContent = statuts['Absent'];
    document.getElementById('count-retard').textContent = statuts['Retard'];
    document.getElementById('count-conge').textContent = statuts['Congé'];
    
    document.getElementById('total-present').textContent = statuts['Présent'];
    document.getElementById('total-absent').textContent = statuts['Absent'];
    document.getElementById('total-retard').textContent = statuts['Retard'];
    
    const total = Object.values(statuts).reduce((a, b) => a + b, 0);
    const tauxPresence = total > 0 ? Math.round((statuts['Présent'] / total) * 100) : 0;
    document.getElementById('taux-presence').textContent = tauxPresence + '%';
}

// Changer de date
function changerDate(jours) {
    const dateInput = document.getElementById('date');
    const currentDate = new Date(dateInput.value);
    currentDate.setDate(currentDate.getDate() + jours);
    dateInput.value = currentDate.toISOString().split('T')[0];
    dateInput.form.submit();
}

// Sauvegarder les présences
function sauvegarderPresences() {
    document.getElementById('presenceForm').submit();
}

// Initialiser les compteurs au chargement
document.addEventListener('DOMContentLoaded', function() {
    mettreAJourCompteurs();
    
    // Ajouter des listeners pour mettre à jour les compteurs
    document.querySelectorAll('.statut-select').forEach(select => {
        select.addEventListener('change', mettreAJourCompteurs);
    });
});
</script>
{% endblock %}
