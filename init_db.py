#!/usr/bin/env python3
"""
Script d'initialisation de la base de données
Crée les tables et insère les données de base
"""

from app import app
from database import db
from models.user import User
from models.production import UniteProduction, Equipement
from models.personnel import Personnel, Competence
from models.stocks import MatierePremiereType, Fournisseur, Stock
from models.qualite import ParametreQualite
from models.maintenance import PieceRechange
from datetime import datetime, date

def init_database():
    """Initialise la base de données avec les données de base"""
    
    with app.app_context():
        # Créer toutes les tables
        db.create_all()
        
        # Vérifier si des données existent déjà
        if User.query.first():
            print("La base de données contient déjà des données.")
            return
        
        print("Initialisation de la base de données...")
        
        # 1. Créer l'utilisateur administrateur
        admin = User(
            username='admin',
            email='<EMAIL>',
            nom='Administrateur',
            prenom='Système',
            role='admin',
            departement='Direction',
            actif=True
        )
        admin.set_password('admin123')
        db.session.add(admin)
        
        # 2. Créer les unités de production
        unite_concassage = UniteProduction(
            nom='Concassage',
            description='Unité de concassage du phosphate brut',
            capacite_nominale=50.0,  # tonnes/heure
            statut='Arrêtée'
        )
        
        unite_lavage = UniteProduction(
            nom='Lavage',
            description='Unité de lavage et purification',
            capacite_nominale=40.0,
            statut='Arrêtée'
        )
        
        unite_eau = UniteProduction(
            nom='Système d\'eau',
            description='Système de traitement et recyclage de l\'eau',
            capacite_nominale=100.0,  # m3/heure
            statut='Arrêtée'
        )
        
        db.session.add_all([unite_concassage, unite_lavage, unite_eau])
        db.session.flush()  # Pour obtenir les IDs
        
        # 3. Créer les équipements
        equipements = [
            # Concassage
            Equipement(
                nom='Concasseur primaire',
                type_equipement='Concasseur',
                numero_serie='CONC-001',
                unite_id=unite_concassage.id,
                statut='Opérationnel',
                date_installation=date(2020, 1, 15)
            ),
            Equipement(
                nom='Concasseur secondaire',
                type_equipement='Concasseur',
                numero_serie='CONC-002',
                unite_id=unite_concassage.id,
                statut='Opérationnel',
                date_installation=date(2020, 2, 10)
            ),
            # Lavage
            Equipement(
                nom='Pompe de lavage 1',
                type_equipement='Pompe',
                numero_serie='PUMP-001',
                unite_id=unite_lavage.id,
                statut='Opérationnel',
                date_installation=date(2020, 3, 5)
            ),
            Equipement(
                nom='Séparateur magnétique',
                type_equipement='Séparateur',
                numero_serie='SEP-001',
                unite_id=unite_lavage.id,
                statut='Opérationnel',
                date_installation=date(2020, 3, 20)
            ),
            # Système d'eau
            Equipement(
                nom='Pompe principale eau',
                type_equipement='Pompe',
                numero_serie='PUMP-002',
                unite_id=unite_eau.id,
                statut='Opérationnel',
                date_installation=date(2020, 4, 1)
            ),
            Equipement(
                nom='Filtre à sable',
                type_equipement='Filtre',
                numero_serie='FILT-001',
                unite_id=unite_eau.id,
                statut='Opérationnel',
                date_installation=date(2020, 4, 15)
            )
        ]
        
        db.session.add_all(equipements)
        
        # 4. Créer le personnel de base
        personnel = [
            Personnel(
                matricule='EMP001',
                nom='Benali',
                prenom='Ahmed',
                poste='Superviseur Production',
                departement='Production',
                niveau_competence='Expert',
                date_embauche=date(2018, 1, 15),
                statut='Actif',
                telephone='0661234567',
                email='<EMAIL>'
            ),
            Personnel(
                matricule='EMP002',
                nom='Alami',
                prenom='Fatima',
                poste='Technicienne Maintenance',
                departement='Maintenance',
                niveau_competence='Intermédiaire',
                date_embauche=date(2019, 3, 10),
                statut='Actif',
                telephone='0662345678',
                email='<EMAIL>'
            ),
            Personnel(
                matricule='EMP003',
                nom='Tazi',
                prenom='Mohamed',
                poste='Opérateur Concassage',
                departement='Production',
                niveau_competence='Intermédiaire',
                date_embauche=date(2020, 6, 1),
                statut='Actif',
                telephone='0663456789'
            ),
            Personnel(
                matricule='EMP004',
                nom='Idrissi',
                prenom='Aicha',
                poste='Contrôleuse Qualité',
                departement='Qualité',
                niveau_competence='Expert',
                date_embauche=date(2017, 9, 15),
                statut='Actif',
                telephone='0664567890',
                email='<EMAIL>'
            )
        ]
        
        db.session.add_all(personnel)
        
        # 5. Créer les compétences
        competences = [
            Competence(
                nom='Conduite d\'équipements',
                description='Capacité à conduire les équipements de production',
                categorie='Technique',
                niveau_requis='Intermédiaire'
            ),
            Competence(
                nom='Maintenance préventive',
                description='Réalisation de la maintenance préventive',
                categorie='Technique',
                niveau_requis='Expert'
            ),
            Competence(
                nom='Sécurité industrielle',
                description='Connaissance des règles de sécurité',
                categorie='Sécurité',
                niveau_requis='Débutant'
            ),
            Competence(
                nom='Contrôle qualité',
                description='Réalisation des contrôles qualité',
                categorie='Technique',
                niveau_requis='Expert'
            )
        ]
        
        db.session.add_all(competences)
        
        # 6. Créer les matières premières
        matieres_premieres = [
            MatierePremiereType(
                nom='Phosphate brut',
                description='Minerai de phosphate non traité',
                unite_mesure='Tonnes',
                densite=2.8,
                specifications='Teneur P2O5 > 28%'
            ),
            MatierePremiereType(
                nom='Eau industrielle',
                description='Eau pour les processus industriels',
                unite_mesure='m3',
                densite=1.0,
                specifications='pH 6.5-8.5, Conductivité < 1000 µS/cm'
            ),
            MatierePremiereType(
                nom='Floculant',
                description='Produit chimique pour la séparation',
                unite_mesure='kg',
                densite=0.9,
                specifications='Polymère anionique'
            )
        ]
        
        db.session.add_all(matieres_premieres)
        db.session.flush()
        
        # 7. Créer les stocks initiaux
        stocks = [
            Stock(
                matiere_premiere_id=matieres_premieres[0].id,  # Phosphate brut
                quantite_actuelle=500.0,
                quantite_minimum=100.0,
                quantite_maximum=1000.0,
                quantite_securite=150.0,
                zone_stockage='Zone A',
                emplacement='A-01',
                prix_moyen=250.0,
                valeur_stock=125000.0
            ),
            Stock(
                matiere_premiere_id=matieres_premieres[1].id,  # Eau industrielle
                quantite_actuelle=50.0,
                quantite_minimum=20.0,
                quantite_maximum=100.0,
                quantite_securite=25.0,
                zone_stockage='Réservoir',
                emplacement='R-01',
                prix_moyen=5.0,
                valeur_stock=250.0
            ),
            Stock(
                matiere_premiere_id=matieres_premieres[2].id,  # Floculant
                quantite_actuelle=200.0,
                quantite_minimum=50.0,
                quantite_maximum=500.0,
                quantite_securite=75.0,
                zone_stockage='Zone B',
                emplacement='B-01',
                prix_moyen=15.0,
                valeur_stock=3000.0
            )
        ]
        
        db.session.add_all(stocks)
        
        # 8. Créer les fournisseurs
        fournisseurs = [
            Fournisseur(
                nom='Mines du Phosphate SA',
                adresse='Khouribga, Maroc',
                telephone='0523456789',
                email='<EMAIL>',
                contact_principal='Hassan Alaoui',
                delai_livraison=7,
                conditions_paiement='30 jours',
                note_qualite=8.5
            ),
            Fournisseur(
                nom='Chimie Industrielle Maroc',
                adresse='Casablanca, Maroc',
                telephone='0522345678',
                email='<EMAIL>',
                contact_principal='Nadia Benali',
                delai_livraison=3,
                conditions_paiement='15 jours',
                note_qualite=9.0
            )
        ]
        
        db.session.add_all(fournisseurs)
        
        # 9. Créer les paramètres de qualité
        parametres_qualite = [
            ParametreQualite(
                nom='Teneur P2O5',
                description='Teneur en pentoxyde de phosphore',
                unite_mesure='%',
                valeur_min=28.0,
                valeur_max=35.0,
                valeur_cible=32.0,
                tolerance=1.0,
                methode_analyse='Spectrophotométrie',
                frequence_controle='Chaque lot'
            ),
            ParametreQualite(
                nom='Humidité',
                description='Taux d\'humidité du produit',
                unite_mesure='%',
                valeur_min=0.0,
                valeur_max=8.0,
                valeur_cible=5.0,
                tolerance=1.0,
                methode_analyse='Étuve',
                frequence_controle='2 fois par jour'
            ),
            ParametreQualite(
                nom='Granulométrie',
                description='Distribution granulométrique',
                unite_mesure='mm',
                valeur_min=0.1,
                valeur_max=5.0,
                valeur_cible=2.0,
                tolerance=0.5,
                methode_analyse='Tamisage',
                frequence_controle='Quotidien'
            )
        ]
        
        db.session.add_all(parametres_qualite)
        
        # 10. Créer les pièces de rechange
        pieces_rechange = [
            PieceRechange(
                nom='Roulement SKF 6308',
                reference='SKF-6308',
                description='Roulement à billes pour moteurs',
                stock_actuel=10,
                stock_minimum=5,
                stock_maximum=20,
                prix_unitaire=85.0,
                fournisseur='SKF Maroc',
                emplacement='Magasin-A1'
            ),
            PieceRechange(
                nom='Courroie trapézoïdale A50',
                reference='BELT-A50',
                description='Courroie pour transmission',
                stock_actuel=8,
                stock_minimum=3,
                stock_maximum=15,
                prix_unitaire=25.0,
                fournisseur='Optibelt Maroc',
                emplacement='Magasin-A2'
            ),
            PieceRechange(
                nom='Joint d\'étanchéité pompe',
                reference='SEAL-P001',
                description='Joint pour pompe centrifuge',
                stock_actuel=15,
                stock_minimum=8,
                stock_maximum=25,
                prix_unitaire=45.0,
                fournisseur='Garlock Maroc',
                emplacement='Magasin-B1'
            )
        ]
        
        db.session.add_all(pieces_rechange)
        
        # Sauvegarder toutes les données
        db.session.commit()
        
        print("Base de données initialisée avec succès!")
        print("\nInformations de connexion:")
        print("Nom d'utilisateur: admin")
        print("Mot de passe: admin123")
        print("\nDonnées créées:")
        print(f"- {len([unite_concassage, unite_lavage, unite_eau])} unités de production")
        print(f"- {len(equipements)} équipements")
        print(f"- {len(personnel)} employés")
        print(f"- {len(competences)} compétences")
        print(f"- {len(matieres_premieres)} matières premières")
        print(f"- {len(stocks)} stocks")
        print(f"- {len(fournisseurs)} fournisseurs")
        print(f"- {len(parametres_qualite)} paramètres de qualité")
        print(f"- {len(pieces_rechange)} pièces de rechange")

if __name__ == '__main__':
    init_database()
