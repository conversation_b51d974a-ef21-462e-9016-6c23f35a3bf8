from datetime import datetime
from database import db

class MatierePremiereType(db.Model):
    """Types de matières premières"""
    __tablename__ = 'matieres_premieres_types'
    
    id = db.Column(db.Integer, primary_key=True)
    nom = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    unite_mesure = db.Column(db.String(20), nullable=False)  # Tonnes, m3, litres, etc.
    densite = db.Column(db.Float)  # kg/m3
    specifications = db.Column(db.Text)  # Spécifications techniques

class Fournisseur(db.Model):
    """Fournisseurs de matières premières"""
    __tablename__ = 'fournisseurs'
    
    id = db.Column(db.Integer, primary_key=True)
    nom = db.Column(db.String(200), nullable=False)
    adresse = db.Column(db.Text)
    telephone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    contact_principal = db.Column(db.String(100))
    
    # Informations commerciales
    delai_livraison = db.Column(db.Integer)  # Jours
    conditions_paiement = db.Column(db.String(100))
    note_qualite = db.Column(db.Float)  # Sur 10
    
    actif = db.Column(db.Boolean, default=True)
    date_creation = db.Column(db.DateTime, default=datetime.utcnow)

class Stock(db.Model):
    """Gestion des stocks"""
    __tablename__ = 'stocks'
    
    id = db.Column(db.Integer, primary_key=True)
    matiere_premiere_id = db.Column(db.Integer, db.ForeignKey('matieres_premieres_types.id'), nullable=False)
    
    # Quantités
    quantite_actuelle = db.Column(db.Float, default=0)
    quantite_minimum = db.Column(db.Float, default=0)
    quantite_maximum = db.Column(db.Float, default=0)
    quantite_securite = db.Column(db.Float, default=0)
    
    # Localisation
    zone_stockage = db.Column(db.String(100))
    emplacement = db.Column(db.String(100))
    
    # Valeurs
    prix_moyen = db.Column(db.Float)  # Prix moyen pondéré
    valeur_stock = db.Column(db.Float)  # Valeur totale du stock
    
    derniere_mise_a_jour = db.Column(db.DateTime, default=datetime.utcnow)
    
    @property
    def besoin_reapprovisionnement(self):
        """Vérifie si un réapprovisionnement est nécessaire"""
        return self.quantite_actuelle <= self.quantite_minimum
    
    @property
    def niveau_critique(self):
        """Vérifie si le stock est à un niveau critique"""
        return self.quantite_actuelle <= self.quantite_securite

class Livraison(db.Model):
    """Livraisons de matières premières"""
    __tablename__ = 'livraisons'
    
    id = db.Column(db.Integer, primary_key=True)
    numero_livraison = db.Column(db.String(50), unique=True, nullable=False)
    fournisseur_id = db.Column(db.Integer, db.ForeignKey('fournisseurs.id'), nullable=False)
    
    # Dates
    date_commande = db.Column(db.Date)
    date_livraison_prevue = db.Column(db.Date)
    date_livraison_reelle = db.Column(db.Date)
    
    # Statut
    statut = db.Column(db.String(50), default='Commandée')  # Commandée, En transit, Livrée, Annulée
    
    # Réception
    receptionnee_par = db.Column(db.Integer, db.ForeignKey('personnel.id'))
    commentaires_reception = db.Column(db.Text)
    
    # Coûts
    cout_total = db.Column(db.Float)
    cout_transport = db.Column(db.Float)
    
    date_creation = db.Column(db.DateTime, default=datetime.utcnow)

class DetailLivraison(db.Model):
    """Détails des livraisons"""
    __tablename__ = 'details_livraison'
    
    id = db.Column(db.Integer, primary_key=True)
    livraison_id = db.Column(db.Integer, db.ForeignKey('livraisons.id'), nullable=False)
    matiere_premiere_id = db.Column(db.Integer, db.ForeignKey('matieres_premieres_types.id'), nullable=False)
    
    # Quantités
    quantite_commandee = db.Column(db.Float, nullable=False)
    quantite_livree = db.Column(db.Float)
    quantite_acceptee = db.Column(db.Float)
    quantite_refusee = db.Column(db.Float, default=0)
    
    # Prix
    prix_unitaire = db.Column(db.Float)
    prix_total = db.Column(db.Float)
    
    # Qualité
    conforme_qualite = db.Column(db.Boolean, default=True)
    observations_qualite = db.Column(db.Text)

class MouvementStock(db.Model):
    """Historique des mouvements de stock"""
    __tablename__ = 'mouvements_stock'
    
    id = db.Column(db.Integer, primary_key=True)
    stock_id = db.Column(db.Integer, db.ForeignKey('stocks.id'), nullable=False)
    
    # Mouvement
    type_mouvement = db.Column(db.String(50), nullable=False)  # Entrée, Sortie, Ajustement
    quantite = db.Column(db.Float, nullable=False)
    quantite_avant = db.Column(db.Float)
    quantite_apres = db.Column(db.Float)
    
    # Référence
    reference_document = db.Column(db.String(100))  # Bon de livraison, bon de sortie, etc.
    motif = db.Column(db.String(200))
    
    # Utilisateur et date
    utilisateur_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    date_mouvement = db.Column(db.DateTime, default=datetime.utcnow)
