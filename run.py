#!/usr/bin/env python3
"""
Script de lancement de l'application Phosphate Manager
"""

import os
import sys
from app import app, db

def create_directories():
    """Créer les répertoires nécessaires"""
    directories = ['uploads', 'reports', 'static/uploads']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Répertoire créé: {directory}")

def check_database():
    """Vérifier si la base de données existe et est initialisée"""
    with app.app_context():
        try:
            # Essayer de créer les tables si elles n'existent pas
            db.create_all()
            
            # Vérifier si des données existent
            from models.user import User
            if not User.query.first():
                print("\n" + "="*50)
                print("ATTENTION: Base de données vide!")
                print("="*50)
                print("Pour initialiser la base de données avec des données de test,")
                print("exécutez la commande suivante dans un autre terminal:")
                print("\n    python init_db.py\n")
                print("Cela créera:")
                print("- Un utilisateur admin (admin/admin123)")
                print("- Des unités de production (Concassage, Lavage, Système d'eau)")
                print("- Des équipements et du personnel de base")
                print("- Des données de stock et de qualité")
                print("="*50)
                
        except Exception as e:
            print(f"Erreur lors de la vérification de la base de données: {e}")

def main():
    """Fonction principale"""
    print("Démarrage de Phosphate Manager...")
    
    # Créer les répertoires nécessaires
    create_directories()
    
    # Vérifier la base de données
    check_database()
    
    # Configuration selon l'environnement
    env = os.environ.get('FLASK_ENV', 'development')
    debug = env == 'development'
    
    print(f"\nEnvironnement: {env}")
    print(f"Mode debug: {debug}")
    print(f"URL de l'application: http://localhost:5000")
    
    if debug:
        print("\nInformations de développement:")
        print("- Rechargement automatique activé")
        print("- Messages de debug activés")
        print("- Base de données SQLite locale")
    
    print("\n" + "="*50)
    print("APPLICATION DÉMARRÉE")
    print("="*50)
    print("Accédez à l'application via: http://localhost:5000")
    print("Pour arrêter l'application: Ctrl+C")
    print("="*50 + "\n")
    
    try:
        # Démarrer l'application
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=debug,
            use_reloader=debug
        )
    except KeyboardInterrupt:
        print("\n\nArrêt de l'application...")
        sys.exit(0)
    except Exception as e:
        print(f"\nErreur lors du démarrage: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
